import pandas as pd
import re
import os
import sys
import tkinter as tk
from tkinter import Toplevel, Entry, Label, Button, messagebox
from tkinter import filedialog

# ==============================================================================
# ČÁST 0: Dialogové okno pro zadání chybějících dat
# ==============================================================================

class InputDialog(Toplevel):
    def __init__(self, parent, title=None, ask_po=False):
        super().__init__(parent)
        self.parent = parent
        self.transient(parent)
        if title: self.title(title)
        self.result = None
        self.body = tk.Frame(self)
        self.initial_focus = self.body_contents(self.body, ask_po)
        self.body.pack(padx=10, pady=10)
        self.buttonbox()
        self.grab_set()
        if not self.initial_focus: self.initial_focus = self
        self.protocol("WM_DELETE_WINDOW", self.cancel)
        self.geometry(f"+{parent.winfo_rootx()+50}+{parent.winfo_rooty()+50}")
        self.initial_focus.focus_set()
        self.wait_window(self)

    def body_contents(self, master, ask_po):
        Label(master, text="Datum dodání (DD.MM.RRRR):").grid(row=0, column=0, sticky="w", pady=2)
        Label(master, text="Číslo zákazníka:").grid(row=1, column=0, sticky="w", pady=2)
        self.entry_date = Entry(master, width=30)
        self.entry_customer = Entry(master, width=30)
        self.entry_date.insert(0, "06.06.2025")
        self.entry_customer.insert(0, "25555308")
        self.entry_date.grid(row=0, column=1, padx=5)
        self.entry_customer.grid(row=1, column=1, padx=5)

        self.ask_po = ask_po
        if self.ask_po:
            Label(master, text="Číslo obj. zákazníka (PO):").grid(row=2, column=0, sticky="w", pady=2)
            self.entry_po = Entry(master, width=30)
            self.entry_po.insert(0, "PO123456")
            self.entry_po.grid(row=2, column=1, padx=5)

        return self.entry_date

    def buttonbox(self):
        box = tk.Frame(self)
        Button(box, text="OK", width=10, command=self.ok, default=tk.ACTIVE).pack(side=tk.LEFT, padx=5, pady=5)
        Button(box, text="Zrušit", width=10, command=self.cancel).pack(side=tk.LEFT, padx=5, pady=5)
        self.bind("<Return>", self.ok)
        self.bind("<Escape>", self.cancel)
        box.pack(pady=10)

    def ok(self, event=None):
        date_val = self.entry_date.get()
        customer_val = self.entry_customer.get()
        po_val = self.entry_po.get() if self.ask_po else None

        if not re.match(r'^\d{2}\.\d{2}\.\d{4}$', date_val):
            messagebox.showerror("Chybný formát", "Datum musí být ve formátu DD.MM.RRRR.")
            return
        if not customer_val or customer_val in ["Číslo", "25555308"]:
            messagebox.showerror("Chybějící údaj", "Zadejte prosím platné číslo zákazníka.")
            return
        if self.ask_po and (not po_val or po_val in ["PO číslo", "PO123456"]):
            messagebox.showerror("Chybějící údaj", "Zadejte prosím číslo objednávky zákazníka (PO).")
            return

        self.result = (date_val, customer_val, po_val)
        self.withdraw()
        self.update_idletasks()
        self.parent.focus_set()
        self.destroy()

    def cancel(self, event=None):
        self.result = None
        self.parent.focus_set()
        self.destroy()

# ==============================================================================
# ČÁST 1: ZPRACOVÁNÍ DAT (JÁDRO PROGRAMU)
# ==============================================================================

def get_documents_folder():
    """Získá cestu ke složce Dokumenty pro aktuálního uživatele"""
    if os.name == 'nt':  # Windows
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                               r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders")
            documents_path = winreg.QueryValueEx(key, "Personal")[0]
            winreg.CloseKey(key)
            return documents_path
        except:
            return os.path.join(os.path.expanduser("~"), "Documents")
    else:
        return os.path.join(os.path.expanduser("~"), "Documents")

def load_data(path):
    """Načte data z CSV nebo Excel souboru."""
    try:
        if path.endswith('.csv'):
            return pd.read_csv(path, sep=';', encoding='windows-1250')
        elif path.endswith(('.xlsx', '.xls')):
            return pd.read_excel(path)
        else:
            raise ValueError("Nepodporovaný formát souboru. Použijte .csv, .xlsx, nebo .xls.")
    except Exception as e:
        raise ValueError(f"Chyba při načítání souboru: {e}")

def process_labara_order(order_path, catalog_path, dated_input, customer_no_input, po_input=None):
    """Zpracuje objednávku Labara."""
    print(f"--- Zpracovávám objednávku Labara: {os.path.basename(order_path)} ---")
    order_items_df = load_data(order_path)
    alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)

    order_items_df.rename(columns={'číslo zboží': 'cislo_zbozi', 'množství': 'mnozstvi', 'název': 'nazev'}, inplace=True)
    order_items_df.reset_index(inplace=True)

    def get_robust_key(text):
        text = str(text)
        material_num_match = re.search(r'(\d{4})', text)
        thickness_match = re.search(r'přířez\s+([\d,]+)', text, re.IGNORECASE)
        if not thickness_match:
            thickness_match = re.search(r'tloušťka\s+([\d,]+)', text, re.IGNORECASE)
        if material_num_match and thickness_match:
            material_num = material_num_match.group(1)
            thickness = float(thickness_match.group(1).replace(',', '.'))
            return f"{material_num}-{thickness:.2f}"
        return None

    def get_catalog_key(text):
        text = str(text)
        material_num_match = re.search(r'(\d{4})', text)
        thickness_match = re.search(r'([\d,]+\.\d{2})', text.replace(',', '.'))
        if material_num_match and thickness_match:
            return f"{material_num_match.group(1)}-{thickness_match.group(1)}"
        return None

    order_items_df['robust_key'] = order_items_df['nazev'].apply(get_robust_key)
    alde_catalog_df['robust_key'] = alde_catalog_df['DESCRIPTION'].apply(get_catalog_key)
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'Popis Položky'}, inplace=True)

    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'Popis Položky', 'robust_key']], on='robust_key', how='left')
    final_df.drop_duplicates(subset=['index'], keep='first', inplace=True)

    unmatched_items = final_df[final_df['CATALOG_NO'].isnull()]
    if not unmatched_items.empty:
        unmatched_list = "\n".join(unmatched_items['nazev'].unique())
        messagebox.showwarning("Nenalezena shoda", f"Pro následující položky nebyla nalezena shoda v katalogu:\n\n{unmatched_list}")

    customer_po_no = os.path.splitext(os.path.basename(order_path))[0]
    final_df['mnozstvi'] = final_df['mnozstvi'].astype(str).str.replace(' ks', '', regex=False).str.strip().astype(int)

    def parse_dimensions(nazev):
        nazev = str(nazev)
        match_dims = re.search(r'-\s*([\d,.]+)\s*x\s*([\d,.]+)', nazev, re.IGNORECASE)
        if match_dims:
            return match_dims.group(1).strip().replace(',', '.'), match_dims.group(2).strip().replace(',', '.')
        return None, None

    dims_data = final_df['nazev'].apply(lambda x: pd.Series(parse_dimensions(x)))
    final_df[['W', 'L']] = dims_data

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': customer_no_input, 'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['mnozstvi'], 'DATED': pd.to_datetime(dated_input, format='%d.%m.%Y').strftime('%d.%m.%Y'),
        'PRICE': 0, 'CURRENCY_CODE': 'CZK', 'CUSTOMER_PO_NO': customer_po_no,
        'W': final_df['W'], 'L': final_df['L']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)
    return output_df

def process_impa_order(order_path, catalog_path, dated_input, customer_no_input, po_input):
    """Zpracuje objednávku IMPA."""
    print(f"--- Zpracovávám objednávku IMPA: {os.path.basename(order_path)} ---")
    order_items_df = load_data(order_path)
    alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
        
    # Přejmenování sloupců pro IMPA formát
    order_items_df.rename(columns={
        'Material': 'material', 
        'Hrubka': 'hrubka', 
        'Sirka': 'W', 
        'Dlzka': 'L', 
        'Pocet kusov': 'QTY'
    }, inplace=True)
    order_items_df.reset_index(inplace=True)
    
    def create_keys(material_text, thickness_val):
        """Vytvoří přesný a volný klíč pro párování materiálů"""
        try:
            text = str(material_text).upper().replace(" ", "").replace("-", "")
            norm_match = re.search(r'(\d{4})', text)
            if not norm_match: 
                return None, None
            norm_part = norm_match.group(1)
            
            # Hledání temper kódu (T6, H14, atd.)
            temper_match = re.search(r'([TH]\d+)', text)
            temper_part = temper_match.group(1) if temper_match else ""
            
            thickness_float = float(str(thickness_val).replace(',', '.'))
            thickness_part = f"{thickness_float:.2f}"
            
            # Přesný klíč s temper kódem, volný bez
            precise_key = f"{norm_part}{temper_part}-{thickness_part}"
            loose_key = f"{norm_part}-{thickness_part}"
            
            return precise_key, loose_key
        except (ValueError, TypeError):
            return None, None

    # Vytvoření klíčů pro objednávku
    keys_df = order_items_df.apply(
        lambda row: pd.Series(create_keys(row['material'], row['hrubka']), index=['precise_key', 'loose_key']), 
        axis=1
    )
    order_items_df = pd.concat([order_items_df, keys_df], axis=1)

    # Příprava katalogu
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'Popis Položky'}, inplace=True)
    
    def get_thickness_from_desc(description):
        """Extrahuje tloušťku z popisu v katalogu"""
        desc_str = str(description)
        thickness_match = re.search(r'([\d,]+\.?\d+)(?:\s*mm)?\s*$', desc_str)
        return thickness_match.group(1) if thickness_match else None

    # Vytvoření klíčů pro katalog
    catalog_keys_df = alde_catalog_df.apply(
        lambda row: pd.Series(create_keys(row['Popis Položky'], get_thickness_from_desc(row['Popis Položky'])), index=['precise_key', 'loose_key']),
        axis=1
    )
    alde_catalog_df = pd.concat([alde_catalog_df, catalog_keys_df], axis=1)
    
    # Odebrání řádků bez klíčů
    order_items_df.dropna(subset=['precise_key', 'loose_key'], how='all', inplace=True)
    alde_catalog_df.dropna(subset=['precise_key', 'loose_key'], how='all', inplace=True)

    # Prvotní párování pomocí přesných klíčů
    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'Popis Položky', 'precise_key']], on='precise_key', how='left')
    
    # Druhé párování pomocí volných klíčů pro nenalezené položky
    unmatched_df = final_df[final_df['CATALOG_NO'].isnull()].copy()
    if not unmatched_df.empty:
        catalog_loose = alde_catalog_df[['CATALOG_NO', 'Popis Položky', 'loose_key']].dropna().drop_duplicates(subset=['loose_key'])
        loose_matches = pd.merge(
            unmatched_df.drop(columns=['CATALOG_NO', 'Popis Položky']), 
            catalog_loose, 
            on='loose_key', 
            how='left'
        )
        final_df.set_index('index', inplace=True)
        final_df.update(loose_matches.set_index('index'))
        final_df.reset_index(inplace=True)

    final_df.drop_duplicates(subset=['index'], keep='first', inplace=True)
    
    # Zobrazení nenalezených položek
    unmatched_items = final_df[final_df['CATALOG_NO'].isnull()]
    if not unmatched_items.empty:
        unmatched_list = "\n".join(unmatched_items['precise_key'].dropna().unique())
        messagebox.showwarning("Nenalezena shoda", f"I po volnějším hledání se pro následující klíče nepodařilo najít shodu:\n\n{unmatched_list}")

    # Vytvoření výstupního DataFrame
    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 
        'CUSTOMER_NO': customer_no_input, 
        'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['QTY'], 
        'DATED': pd.to_datetime(dated_input, format='%d.%m.%Y').strftime('%d.%m.%Y'),
        'PRICE': 0, 
        'CURRENCY_CODE': 'CZK', 
        'CUSTOMER_PO_NO': po_input,
        'W': final_df['W'], 
        'L': final_df['L']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)
    return output_df

def process_descon_order(order_path, catalog_path):
    """Zpracuje objednávku Descon."""
    print(f"--- Zpracovávám objednávku Descon: {os.path.basename(order_path)} ---")
    try:
        order_items_df = pd.read_excel(order_path, sheet_name=1, header=0, skipfooter=3)
        alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
    except Exception as e:
        raise ValueError(f"Chyba při načítání dat pro Descon.\n\nDetail chyby: {e}")

    order_items_df = order_items_df[order_items_df['CisloDilu'].str.contains('_', na=False)]
    typ_column_name = order_items_df.columns[3]
    date_column_name = order_items_df.columns[-1]

    def create_search_key(row):
        material = row['MaterialVyroba']; typ = row[typ_column_name]; thickness = row['Tloustka']
        base_material = ""
        if "5083" in material: base_material = "5083"
        elif "6082" in material: base_material = "ENAW6082T651"
        if "frézovaná" in str(typ): base_material += " litá frézovaná"
        elif "litá" in str(typ): base_material += " litá"
        if not base_material or pd.isna(thickness): return None
        return f"{base_material} {float(thickness):.2f}".replace('.', ',')

    order_items_df['search_key'] = order_items_df.apply(create_search_key, axis=1)
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'search_key'}, inplace=True)
    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'search_key']], on='search_key', how='left')

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': '505992', 'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['KS'], 'DATED': pd.to_datetime(final_df[date_column_name].iloc[0]).strftime('%d.%m.%Y'),
        'PRICE': 0, 'CURRENCY_CODE': 'CZK', 'CUSTOMER_PO_NO': final_df['Zakazka'],
        'W': final_df['SirkaPrumer'], 'L': final_df['Delka']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)
    return output_df

# ==============================================================================
# ČÁST 2: GRAFICKÉ ROZHRANÍ (GUI)
# ==============================================================================

def get_script_directory():
    """Získá adresář, kde se nachází script nebo executable"""
    if getattr(sys, 'frozen', False):
        # Pokud je spuštěn jako executable (PyInstaller)
        return os.path.dirname(sys.executable)
    else:
        # Pokud je spuštěn jako Python script
        return os.path.dirname(os.path.abspath(__file__))

def run_processing(root, process_func, title, filetypes, ask_po=False):
    """Univerzální funkce pro spuštění zpracování objednávek"""
    filepath = filedialog.askopenfilename(title=title, filetypes=filetypes)
    if not filepath: 
        return

    # Dialog pro zadání chybějících údajů
    dialog = InputDialog(root, "Doplňte chybějící údaje", ask_po=ask_po)
    if not dialog.result: 
        print("Zpracování zrušeno uživatelem.")
        return
    
    dated, customer_no, po_no = dialog.result

    try:
        # Nalezení katalogového souboru
        script_dir = get_script_directory()
        catalog_path = os.path.join(script_dir, 'ALDE_katalog_položek.xlsx')
        
        if not os.path.exists(catalog_path):
            messagebox.showerror("Chyba souboru", f"Katalogový soubor 'ALDE_katalog_položek.xlsx' nebyl nalezen v adresáři:\n{script_dir}")
            return

        # Zpracování podle typu objednávky
        po_for_filename = po_no
        
        if process_func == process_labara_order:
            output_df = process_func(filepath, catalog_path, dated, customer_no)
            po_for_filename = os.path.splitext(os.path.basename(filepath))[0]
        elif process_func == process_descon_order:
            output_df = process_func(filepath, catalog_path)
            if output_df.empty: 
                return
            po_for_filename = "descon_output"
        else:  # IMPA
            output_df = process_func(filepath, catalog_path, dated, customer_no, po_no)

        if output_df.empty:
            messagebox.showwarning("Prázdný výstup", "Po zpracování nebyla nalezena žádná platná data k uložení.")
            return
        
        # Uložení výstupu
        safe_filename = re.sub(r'[\\/*?:"<>|]', "", str(po_for_filename))
        
        # Vytvoření výstupní složky v Documents
        documents_folder = get_documents_folder()
        output_folder = os.path.join(documents_folder, 'Migrace_Vystupy')
        if not os.path.exists(output_folder): 
            os.makedirs(output_folder)
        
        output_filename = f"vystup_{safe_filename}.csv"
        output_path = os.path.join(output_folder, output_filename)
        
        # Uložení s UTF-8 BOM pro správné zobrazení českých znaků
        output_df.to_csv(output_path, index=False, sep=';', encoding='utf-8-sig')
        messagebox.showinfo("Hotovo", f"Zpracování dokončeno!\n\nVýstup byl uložen do:\n{output_path}")

    except Exception as e:
        messagebox.showerror("Chyba při zpracování", f"Vyskytla se neočekávaná chyba:\n\n{e}")
        import traceback
        traceback.print_exc()

# ==============================================================================
# ČÁST 3: HLAVNÍ PROGRAM
# ==============================================================================

def main():
    """Hlavní funkce aplikace"""
    root = tk.Tk()
    root.title("Nástroj pro migraci objednávek v7.1 - KOMPLETNÍ EDICE")
    root.geometry("500x300")
    root.resizable(False, False)
    
    # Hlavní frame
    main_frame = tk.Frame(root, padx=20, pady=20)
    main_frame.pack(expand=True, fill=tk.BOTH)
    
    # Nadpis
    label = tk.Label(main_frame, text="Vyberte typ objednávky ke zpracování:", font=("Segoe UI", 12, "bold"))
    label.pack(pady=(0, 20))

    # Definice typů souborů
    filetypes_all = [("Podporované soubory", "*.csv *.xlsx *.xls"), ("Všechny soubory", "*.*")]
    filetypes_excel = [("Excel soubory", "*.xlsx *.xls"), ("Všechny soubory", "*.*")]

    # Tlačítko pro LABARA
    btn_labara = tk.Button(main_frame, text="Zpracovat objednávku LABARA", 
        command=lambda: run_processing(root, process_labara_order, "Vyberte objednávku LABARA", filetypes_all), 
        width=40, height=2, bg='#E3F2FD', activebackground='#BBDEFB')
    btn_labara.pack(pady=5)
    
    # Tlačítko pro IMPA - KONEČNĚ PŘIDÁNO!
    btn_impa = tk.Button(main_frame, text="Zpracovat objednávku IMPA", 
        command=lambda: run_processing(root, process_impa_order, "Vyberte objednávku IMPA", filetypes_excel, ask_po=True), 
        width=40, height=2, bg='#D4EDDA', activebackground='#C3E6CB')
    btn_impa.pack(pady=5)

    # Tlačítko pro DESCON
    btn_descon = tk.Button(main_frame, text="Zpracovat objednávku DESCON", 
        command=lambda: run_processing(root, process_descon_order, "Vyberte objednávku DESCON", filetypes_excel), 
        width=40, height=2, bg='#FFF3CD', activebackground='#FFEAA7')
    btn_descon.pack(pady=5)
    
    # Informační text
    info_label = tk.Label(main_frame, text="IMPA: Použije PO číslo ze zadání\nLABARA/DESCON: Použije název souboru jako PO", 
                         font=("Segoe UI", 9), fg="gray")
    info_label.pack(pady=(15, 0))
    
    root.mainloop()

if __name__ == "__main__":
    main()