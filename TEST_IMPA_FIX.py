#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST IMPA OPRAVY
================
Testovací script pro ověření oprav IMPA zpracování
"""

import pandas as pd
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_standalone_final import process_impa_order
    print("✅ Modul úspěš<PERSON>ě načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def create_test_impa_excel():
    """Vytvoří testovací IMPA Excel soubor."""
    test_data = {
        'Material': ['5083', '6061T651', 'ENAW5754H111', '7075', '5083', 'NeznámýMat'],
        'Hrubka': [10.0, 25.0, 30.0, 15.0, 20.0, 40.0],
        'Sirka': [80, 210, 65, 150, 100, 200],
        'Dlzka': [90, 570, 135, 300, 200, 400],
        '<PERSON>cet kusov': [3, 3, 4, 4, 2, 1]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_impa_fix.xlsx'
    df.to_excel(test_file, index=False)
    print(f"✅ Vytvořen testovací IMPA soubor: {test_file}")
    return test_file

def test_impa_processing():
    """Testuje IMPA zpracování."""
    print("\n" + "="*60)
    print("🧪 TESTOVÁNÍ IMPA ZPRACOVÁNÍ")
    print("="*60)
    
    # Vytvoř testovací soubor
    test_file = create_test_impa_excel()
    
    try:
        # Test zpracování
        print(f"\n📊 Testování souboru: {test_file}")
        result_df = process_impa_order(
            order_path=test_file,
            dated_input="01.08.2025", 
            customer_no_input="504398",
            po_input="IMPA_TEST_2025"
        )
        
        print(f"\n✅ VÝSLEDEK:")
        print(f"   • Zpracováno: {len(result_df)} položek")
        
        if len(result_df) > 0:
            print(f"   • Sloupce: {list(result_df.columns)}")
            print(f"\n📋 UKÁZKA VÝSTUPU:")
            print(result_df.to_string(index=False))
            
            # Uložení výsledku
            output_file = 'test_impa_output.csv'
            result_df.to_csv(output_file, index=False, sep=';', encoding='utf-8-sig')
            print(f"\n💾 Výstup uložen do: {output_file}")
        else:
            print("   ❌ ŽÁDNÉ PLATNÉ POLOŽKY!")
            
    except Exception as e:
        print(f"❌ CHYBA PŘI ZPRACOVÁNÍ: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Úklid
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 Smazán testovací soubor: {test_file}")

if __name__ == "__main__":
    print("🚀 SPOUŠTÍM TEST IMPA OPRAV")
    print("="*60)
    
    # Test IMPA zpracování
    test_impa_processing()
    
    print("\n" + "="*60)
    print("✅ TEST DOKONČEN")
    print("="*60)
    
    input("\nStiskněte Enter pro ukončení...")
