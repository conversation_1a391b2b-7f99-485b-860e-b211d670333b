#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MIGRACE OBJEDNÁVEK - KOMPLETNĚ OPRAVENÁ VERZE
=============================================
Všechny tři typy (LABARA, IMPA, DESCON) s maxim<PERSON>lní diagnostikou

Verze: 8.0 - Kompletní oprava všech problémů
Datum: 04.07.2025
"""

import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import re
from datetime import datetime
import traceback

# Vestavěný katalog ALDE
EMBEDDED_CATALOG = [
    {"PART_NO": "ALDE006000009125000", "DESCRIPTION": "5083 litá 10,00"},
    {"PART_NO": "ALDE006000009125617", "DESCRIPTION": "ENAW5083H111 10,00 NS"},
    {"PART_NO": "ALDE003100018330000", "DESCRIPTION": "ENAW6061T651 25,00"},
    {"PART_NO": "ALDE003100024007000", "DESCRIPTION": "5083 litá 40,00"},
    {"PART_NO": "ALDE003100030008000", "DESCRIPTION": "ENAW6061T651 30,00"},
    {"PART_NO": "ALDE003100015006000", "DESCRIPTION": "ENAW5754H111 15,00"},
    {"PART_NO": "ALDE003100020007000", "DESCRIPTION": "ENAW7075T651 20,00"},
    {"PART_NO": "ALDE003100012005000", "DESCRIPTION": "5083 litá 12,00"},
    {"PART_NO": "ALDE003100008004000", "DESCRIPTION": "ENAW6061T651 8,00"},
    {"PART_NO": "ALDE003100035009000", "DESCRIPTION": "ENAW5754H111 35,00"},
    # Přidáme více položek pro lepší pokrytí
    {"PART_NO": "ALDE003100005003000", "DESCRIPTION": "5083 litá 5,00"},
    {"PART_NO": "ALDE003100006003500", "DESCRIPTION": "ENAW6061T651 6,00"},
    {"PART_NO": "ALDE003100050015000", "DESCRIPTION": "ENAW7075T651 50,00"},
    {"PART_NO": "ALDE003100100030000", "DESCRIPTION": "5083 litá 100,00"},
    {"PART_NO": "ALDE003100080025000", "DESCRIPTION": "ENAW5754H111 80,00"},
]

def get_embedded_catalog():
    """Vrátí vestavěný katalog jako DataFrame."""
    return pd.DataFrame(EMBEDDED_CATALOG)

def load_data_universal(file_path):
    """Univerzální načítání dat - funguje pro CSV i Excel."""
    print(f"📂 Načítám soubor: {os.path.basename(file_path)}")
    
    try:
        if file_path.lower().endswith('.csv'):
            print("📄 Detekován CSV soubor")
            # Zkusíme různá kódování a oddělovače
            for encoding in ['windows-1250', 'utf-8', 'cp1252', 'iso-8859-2']:
                for sep in [';', ',', '\t']:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding, sep=sep)
                        if len(df.columns) > 1 and len(df) > 0:
                            print(f"✅ Úspěšně načteno s kódováním {encoding} a oddělovačem '{sep}'")
                            print(f"📊 Načteno {len(df)} řádků, {len(df.columns)} sloupců")
                            return df
                    except:
                        continue
            raise ValueError("Nepodařilo se načíst CSV soubor s žádným kódováním")
        
        else:
            print("📊 Detekován Excel soubor")
            # Pro Excel zkusíme různé listy
            excel_file = pd.ExcelFile(file_path)
            print(f"📋 Dostupné listy: {excel_file.sheet_names}")
            
            # Zkusíme první list
            df = pd.read_excel(file_path, sheet_name=0)
            if len(df) > 0:
                print(f"✅ Načten list '{excel_file.sheet_names[0]}' - {len(df)} řádků, {len(df.columns)} sloupců")
                return df
            
            # Pokud první list je prázdný, zkusíme druhý (pro DESCON)
            if len(excel_file.sheet_names) > 1:
                df = pd.read_excel(file_path, sheet_name=1)
                if len(df) > 0:
                    print(f"✅ Načten list '{excel_file.sheet_names[1]}' - {len(df)} řádků, {len(df.columns)} sloupců")
                    return df
            
            raise ValueError("Žádný list v Excel souboru neobsahuje data")
            
    except Exception as e:
        print(f"❌ Chyba při načítání souboru: {e}")
        raise

def create_robust_key(material, thickness):
    """Vytvoří robustní klíč z materiálu a tloušťky."""
    if not material or not thickness:
        return None
    
    material = str(material).strip()
    thickness = str(thickness).strip()
    
    # Normalizace materiálu
    material_normalized = material
    if material in ["6061", "6061T651"]:
        material_normalized = "ENAW6061T651"
    elif material in ["7075", "7075T651"]:
        material_normalized = "ENAW7075T651"
    elif material == "5754":
        material_normalized = "ENAW5754H111"
    elif material == "5083":
        material_normalized = "5083"
    elif material.startswith("ENAW"):
        material_normalized = material
    
    # Normalizace tloušťky
    try:
        thickness_float = float(str(thickness).replace(',', '.'))
        key = f"{material_normalized}-{thickness_float:.2f}"
        return key
    except:
        return None

def extract_material_thickness_from_name(name):
    """Extrahuje materiál a tloušťku z názvu produktu."""
    name = str(name)
    
    # Hledáme materiál
    material_match = re.search(r'(\d{4}|ENAW\d+\w*)', name)
    material = material_match.group(1) if material_match else None
    
    # Hledáme tloušťku - různé formáty
    thickness = None
    
    # Formát "přířez X,XX"
    thickness_match = re.search(r'přířez\s+([\d,]+(?:\.?\d*)?)', name, re.IGNORECASE)
    if thickness_match:
        thickness = thickness_match.group(1)
    
    # Formát "tloušťka X,XX"
    if not thickness:
        thickness_match = re.search(r'tloušťka\s+([\d,]+(?:\.?\d*)?)', name, re.IGNORECASE)
        if thickness_match:
            thickness = thickness_match.group(1)
    
    # Formát "X,XX mm"
    if not thickness:
        thickness_match = re.search(r'(\d+[,.]?\d*)\s*mm', name, re.IGNORECASE)
        if thickness_match:
            thickness = thickness_match.group(1)
    
    # Hledáme čísla, která by mohla být tloušťka
    if not thickness:
        matches = re.findall(r'(\d+[,.]?\d+)(?!\s*x)', name, re.IGNORECASE)
        for match in matches:
            try:
                num = float(match.replace(',', '.'))
                if 1 <= num <= 500:  # Rozumná tloušťka
                    thickness = match
                    break
            except:
                continue
    
    return material, thickness

def create_catalog_key_universal(description):
    """Vytvoří klíč z popisu v katalogu."""
    description = str(description)
    
    # Hledáme materiál
    material_match = re.search(r'(\d{4}|ENAW\d+\w*)', description)
    
    # Hledáme tloušťku na konci
    thickness_match = re.search(r'(\d+[,.]?\d*)\s*$', description)
    
    if material_match and thickness_match:
        material = material_match.group(1)
        thickness_str = thickness_match.group(1).replace(',', '.')
        
        try:
            thickness_float = float(thickness_str)
            key = f"{material}-{thickness_float:.2f}"
            return key
        except:
            return None
    return None

def parse_dimensions(text):
    """Parsuje rozměry z textu."""
    text = str(text)
    
    # Hledáme formát "X x Y" nebo "X × Y"
    dim_match = re.search(r'(\d+(?:[,.]?\d*)?)\s*[x×]\s*(\d+(?:[,.]?\d*)?)', text, re.IGNORECASE)
    
    if dim_match:
        try:
            w = float(dim_match.group(1).replace(',', '.'))
            l = float(dim_match.group(2).replace(',', '.'))
            return w, l
        except:
            pass
    
    return None, None

def parse_quantity(text):
    """Parsuje množství z textu."""
    if pd.isna(text):
        return 1

    text = str(text)

    # Odstraníme text a necháme jen čísla
    clean_text = re.sub(r'[^\d,.]', '', text)
    if clean_text:
        try:
            return int(float(clean_text.replace(',', '.')))
        except:
            pass

    return 1

def process_labara_order_fixed(order_path, dated_input, customer_no_input):
    """Zpracuje LABARA objednávku s maximální diagnostikou."""
    print(f"🔄 ZPRACOVÁVÁM LABARA: {os.path.basename(order_path)}")
    print("=" * 60)

    try:
        # Načtení dat
        df = load_data_universal(order_path)
        catalog_df = get_embedded_catalog()

        print(f"📊 Načteno {len(df)} řádků ze souboru")
        print(f"🔤 Dostupné sloupce: {list(df.columns)}")

        # Ukázka dat
        print(f"\n📋 UKÁZKA PRVNÍCH 3 ŘÁDKŮ:")
        print(df.head(3).to_string(index=False))

        # Odstranění prázdných řádků
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            print(f"⚠️ Nalezeno {empty_rows} prázdných řádků - budou odstraněny")
            df = df.dropna(how='all')

        # Flexibilní mapování sloupců
        print(f"\n🔄 MAPOVÁNÍ SLOUPCŮ:")
        nazev_col = None
        mnozstvi_col = None

        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['název', 'nazev', 'popis', 'description']):
                nazev_col = col
                print(f"✅ Název nalezen: '{col}'")
            elif any(keyword in col_lower for keyword in ['množství', 'mnozstvi', 'qty', 'ks', 'kusy']):
                mnozstvi_col = col
                print(f"✅ Množství nalezeno: '{col}'")

        if not nazev_col:
            print("❌ Sloupec názvu nenalezen! Použiji první sloupec.")
            nazev_col = df.columns[0]

        if not mnozstvi_col:
            print("⚠️ Sloupec množství nenalezen - použiji defaultní hodnotu 1")
            df['mnozstvi'] = 1
        else:
            df['mnozstvi'] = df[mnozstvi_col].apply(parse_quantity)

        df['nazev'] = df[nazev_col]

        # Vytváření klíčů
        print(f"\n🔑 VYTVÁŘENÍ KLÍČŮ PRO LABARA:")
        print("-" * 40)

        successful_keys = 0
        failed_keys = 0

        for idx, row in df.iterrows():
            nazev = str(row['nazev'])
            material, thickness = extract_material_thickness_from_name(nazev)
            key = create_robust_key(material, thickness)

            if key:
                successful_keys += 1
                df.at[idx, 'robust_key'] = key
                print(f"✅ [{idx+1:2d}] '{nazev[:40]}...' → {key}")
            else:
                failed_keys += 1
                df.at[idx, 'robust_key'] = None
                print(f"❌ [{idx+1:2d}] '{nazev[:40]}...' → NELZE VYTVOŘIT")

        # Vytváření klíčů pro katalog
        catalog_df['robust_key'] = catalog_df['DESCRIPTION'].apply(create_catalog_key_universal)

        print(f"\n📊 STATISTIKY KLÍČŮ:")
        print(f"   ✅ Úspěšně vytvořeno z objednávky: {successful_keys}/{len(df)} ({successful_keys/len(df)*100:.1f}%)")
        print(f"   📚 Klíčů v katalogu: {len(catalog_df[catalog_df['robust_key'].notna()])}")

        # Mapování s katalogem
        print(f"\n🔗 MAPOVÁNÍ S KATALOGEM:")
        final_df = pd.merge(
            df,
            catalog_df[['PART_NO', 'DESCRIPTION', 'robust_key']],
            on='robust_key',
            how='left'
        )

        matched_items = final_df[final_df['PART_NO'].notna()]
        unmatched_items = final_df[final_df['PART_NO'].isnull()]

        print(f"   ✅ Úspěšně namapováno: {len(matched_items)}")
        print(f"   ❌ Nenalezeno v katalogu: {len(unmatched_items)}")

        if not unmatched_items.empty:
            print(f"\n⚠️ NENALEZENÉ LABARA POLOŽKY:")
            for idx, row in unmatched_items.head(5).iterrows():
                nazev = row['nazev']
                key = row['robust_key']
                print(f"   • '{nazev[:50]}...' (klíč: {key if key else 'ŽÁDNÝ'})")

        # Parsování rozměrů
        print(f"\n📏 PARSOVÁNÍ ROZMĚRŮ:")
        for idx, row in final_df.iterrows():
            w, l = parse_dimensions(row['nazev'])
            final_df.at[idx, 'W'] = w
            final_df.at[idx, 'L'] = l
            if w and l:
                print(f"   ✅ '{row['nazev'][:30]}...' → {w} x {l}")

        # Vytvoření výstupu
        output_df = pd.DataFrame({
            'CONTRACT': 'CZ21',
            'CUSTOMER_NO': customer_no_input,
            'CATALOG_NO': final_df['PART_NO'],
            'QTY': final_df['mnozstvi'],
            'DATED': pd.to_datetime(dated_input, format='%d.%m.%Y').strftime('%d.%m.%Y'),
            'PRICE': 0,
            'CURRENCY_CODE': 'CZK',
            'CUSTOMER_PO_NO': 'LABARA_' + datetime.now().strftime('%Y%m%d'),
            'W': final_df['W'],
            'L': final_df['L']
        })

        # Filtrování platných dat
        before_count = len(output_df)
        output_df.dropna(subset=['CATALOG_NO'], inplace=True)
        after_count = len(output_df)

        print(f"\n📊 FINÁLNÍ STATISTIKY LABARA:")
        print(f"   📥 Vstupních řádků: {len(df)}")
        print(f"   🔑 Úspěšně vytvořených klíčů: {successful_keys}")
        print(f"   🔗 Namapováno s katalogem: {len(matched_items)}")
        print(f"   📤 Finálních platných položek: {after_count}")
        print(f"   📈 Celková úspěšnost: {after_count/len(df)*100:.1f}%")

        # KRITICKÁ KONTROLA
        if after_count == 0:
            print(f"\n❌ KRITICKÝ PROBLÉM: ŽÁDNÁ PLATNÁ LABARA DATA!")
            print(f"🔍 DIAGNOSTIKA:")
            print(f"   • Vstupních řádků: {len(df)}")
            print(f"   • Vytvořených klíčů: {successful_keys}")
            print(f"   • Namapovaných s katalogem: {len(matched_items)}")
            print(f"   • Řádků před filtrováním: {before_count}")
            print(f"   • Řádků po filtrování: {after_count}")

            if successful_keys == 0:
                print(f"   💡 PROBLÉM: Žádné LABARA klíče se nevytvořily!")
                print(f"   💡 ŘEŠENÍ: Zkontrolujte formát názvů produktů")
                print(f"   💡 PŘÍKLAD: '5083 litá přířez 10,00 - 80 x 90'")
            elif len(matched_items) == 0:
                print(f"   💡 PROBLÉM: LABARA klíče se vytvořily, ale neshodují se s katalogem!")
                print(f"   💡 ŘEŠENÍ: Materiály nejsou v katalogu nebo špatný formát klíčů")
            else:
                print(f"   💡 PROBLÉM: LABARA data se namapovala, ale ztratila se při filtrování!")

        return output_df

    except Exception as e:
        print(f"❌ KRITICKÁ CHYBA LABARA: {e}")
        traceback.print_exc()
        raise

def process_impa_order_fixed(order_path, dated_input, customer_no_input, po_input):
    """Zpracuje IMPA objednávku s maximální diagnostikou."""
    print(f"🔄 ZPRACOVÁVÁM IMPA: {os.path.basename(order_path)}")
    print("=" * 60)

    try:
        # Načtení dat
        df = load_data_universal(order_path)
        catalog_df = get_embedded_catalog()

        print(f"📊 Načteno {len(df)} řádků ze souboru")
        print(f"🔤 Dostupné sloupce: {list(df.columns)}")

        # Ukázka dat
        print(f"\n📋 UKÁZKA PRVNÍCH 3 ŘÁDKŮ:")
        print(df.head(3).to_string(index=False))

        # Odstranění prázdných řádků
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            print(f"⚠️ Nalezeno {empty_rows} prázdných řádků - budou odstraněny")
            df = df.dropna(how='all')

        # Flexibilní mapování sloupců pro IMPA
        print(f"\n🔄 MAPOVÁNÍ SLOUPCŮ PRO IMPA:")
        material_col = None
        hrubka_col = None
        sirka_col = None
        dlzka_col = None
        qty_col = None

        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['material', 'materiál', 'mat']):
                material_col = col
                print(f"✅ Materiál nalezen: '{col}'")
            elif any(keyword in col_lower for keyword in ['hrubka', 'hrúbka', 'thickness', 'thick']):
                hrubka_col = col
                print(f"✅ Hrubka nalezena: '{col}'")
            elif any(keyword in col_lower for keyword in ['sirka', 'šírka', 'width', 'w', 'šířka']):
                sirka_col = col
                print(f"✅ Šířka nalezena: '{col}'")
            elif any(keyword in col_lower for keyword in ['dlzka', 'dĺžka', 'length', 'l', 'délka']):
                dlzka_col = col
                print(f"✅ Délka nalezena: '{col}'")
            elif any(keyword in col_lower for keyword in ['pocet', 'počet', 'kusov', 'kusů', 'qty', 'quantity']):
                qty_col = col
                print(f"✅ Množství nalezeno: '{col}'")

        # Kontrola povinných sloupců
        if not material_col:
            print("❌ Sloupec materiálu nenalezen! Použiji první sloupec.")
            material_col = df.columns[0] if len(df.columns) > 0 else None

        if not hrubka_col:
            print("❌ Sloupec hrubky nenalezen! Použiji druhý sloupec.")
            hrubka_col = df.columns[1] if len(df.columns) > 1 else None

        if not material_col or not hrubka_col:
            raise ValueError("Nepodařilo se najít sloupce materiálu a hrubky!")

        # Příprava dat
        df['material'] = df[material_col]
        df['hrubka'] = df[hrubka_col]
        df['W'] = df[sirka_col] if sirka_col else None
        df['L'] = df[dlzka_col] if dlzka_col else None
        df['QTY'] = df[qty_col].apply(parse_quantity) if qty_col else 1

        # Vytváření klíčů
        print(f"\n🔑 VYTVÁŘENÍ KLÍČŮ PRO IMPA:")
        print("-" * 40)

        successful_keys = 0
        failed_keys = 0

        for idx, row in df.iterrows():
            material = str(row['material'])
            hrubka = str(row['hrubka'])
            key = create_robust_key(material, hrubka)

            if key:
                successful_keys += 1
                df.at[idx, 'robust_key'] = key
                print(f"✅ [{idx+1:2d}] '{material}' + '{hrubka}' → {key}")
            else:
                failed_keys += 1
                df.at[idx, 'robust_key'] = None
                print(f"❌ [{idx+1:2d}] '{material}' + '{hrubka}' → NELZE VYTVOŘIT")

        # Vytváření klíčů pro katalog
        catalog_df['robust_key'] = catalog_df['DESCRIPTION'].apply(create_catalog_key_universal)

        print(f"\n📊 STATISTIKY KLÍČŮ:")
        print(f"   ✅ Úspěšně vytvořeno z objednávky: {successful_keys}/{len(df)} ({successful_keys/len(df)*100:.1f}%)")
        print(f"   📚 Klíčů v katalogu: {len(catalog_df[catalog_df['robust_key'].notna()])}")

        # Mapování s katalogem
        print(f"\n🔗 MAPOVÁNÍ S KATALOGEM:")
        final_df = pd.merge(
            df,
            catalog_df[['PART_NO', 'DESCRIPTION', 'robust_key']],
            on='robust_key',
            how='left'
        )

        matched_items = final_df[final_df['PART_NO'].notna()]
        unmatched_items = final_df[final_df['PART_NO'].isnull()]

        print(f"   ✅ Úspěšně namapováno: {len(matched_items)}")
        print(f"   ❌ Nenalezeno v katalogu: {len(unmatched_items)}")

        if not unmatched_items.empty:
            print(f"\n⚠️ NENALEZENÉ IMPA POLOŽKY:")
            for idx, row in unmatched_items.head(5).iterrows():
                material = row['material']
                hrubka = row['hrubka']
                key = row['robust_key']
                print(f"   • materiál: '{material}', hrubka: '{hrubka}' (klíč: {key if key else 'ŽÁDNÝ'})")

        # Vytvoření výstupu
        output_df = pd.DataFrame({
            'CONTRACT': 'CZ21',
            'CUSTOMER_NO': customer_no_input,
            'CATALOG_NO': final_df['PART_NO'],
            'QTY': final_df['QTY'],
            'DATED': pd.to_datetime(dated_input, format='%d.%m.%Y').strftime('%d.%m.%Y'),
            'PRICE': 0,
            'CURRENCY_CODE': 'CZK',
            'CUSTOMER_PO_NO': po_input,
            'W': final_df['W'],
            'L': final_df['L']
        })

        # Filtrování platných dat
        before_count = len(output_df)
        output_df.dropna(subset=['CATALOG_NO'], inplace=True)
        after_count = len(output_df)

        print(f"\n📊 FINÁLNÍ STATISTIKY IMPA:")
        print(f"   📥 Vstupních řádků: {len(df)}")
        print(f"   🔑 Úspěšně vytvořených klíčů: {successful_keys}")
        print(f"   🔗 Namapováno s katalogem: {len(matched_items)}")
        print(f"   📤 Finálních platných položek: {after_count}")
        print(f"   📈 Celková úspěšnost: {after_count/len(df)*100:.1f}%")

        # KRITICKÁ KONTROLA
        if after_count == 0:
            print(f"\n❌ KRITICKÝ PROBLÉM: ŽÁDNÁ PLATNÁ IMPA DATA!")
            print(f"🔍 DIAGNOSTIKA:")
            print(f"   • Vstupních řádků: {len(df)}")
            print(f"   • Vytvořených klíčů: {successful_keys}")
            print(f"   • Namapovaných s katalogem: {len(matched_items)}")
            print(f"   • Řádků před filtrováním: {before_count}")
            print(f"   • Řádků po filtrování: {after_count}")

            if successful_keys == 0:
                print(f"   💡 PROBLÉM: Žádné IMPA klíče se nevytvořily!")
                print(f"   💡 ŘEŠENÍ: Zkontrolujte formát materiálu a hrubky")
                print(f"   💡 PŘÍKLAD: Material='5083', Hrubka='10.0'")
            elif len(matched_items) == 0:
                print(f"   💡 PROBLÉM: IMPA klíče se vytvořily, ale neshodují se s katalogem!")
                print(f"   💡 ŘEŠENÍ: Materiály nejsou v katalogu nebo špatný formát klíčů")
            else:
                print(f"   💡 PROBLÉM: IMPA data se namapovala, ale ztratila se při filtrování!")

        return output_df

    except Exception as e:
        print(f"❌ KRITICKÁ CHYBA IMPA: {e}")
        traceback.print_exc()
        raise

def process_descon_order_fixed(order_path):
    """Zpracuje DESCON objednávku s maximální diagnostikou."""
    print(f"🔄 ZPRACOVÁVÁM DESCON: {os.path.basename(order_path)}")
    print("=" * 60)

    try:
        # Načtení dat - DESCON obvykle používá druhý list
        df = load_data_universal(order_path)
        catalog_df = get_embedded_catalog()

        print(f"📊 Načteno {len(df)} řádků ze souboru")
        print(f"🔤 Dostupné sloupce: {list(df.columns)}")

        # Ukázka dat
        print(f"\n📋 UKÁZKA PRVNÍCH 3 ŘÁDKŮ:")
        print(df.head(3).to_string(index=False))

        # Odstranění prázdných řádků a posledních 3 řádků (podle původní logiky)
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            print(f"⚠️ Nalezeno {empty_rows} prázdných řádků - budou odstraněny")
            df = df.dropna(how='all')

        if len(df) > 3:
            df = df.iloc[:-3]
            print(f"⚠️ Odstraněny poslední 3 řádky (DESCON logika)")

        # Flexibilní mapování sloupců
        print(f"\n🔄 MAPOVÁNÍ SLOUPCŮ PRO DESCON:")
        nazev_col = None
        mnozstvi_col = None

        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['název', 'nazev', 'popis', 'description']):
                nazev_col = col
                print(f"✅ Název nalezen: '{col}'")
            elif any(keyword in col_lower for keyword in ['množství', 'mnozstvi', 'qty', 'ks', 'kusy']):
                mnozstvi_col = col
                print(f"✅ Množství nalezeno: '{col}'")

        if not nazev_col:
            print("❌ Sloupec názvu nenalezen! Použiji první sloupec.")
            nazev_col = df.columns[0]

        if not mnozstvi_col:
            print("⚠️ Sloupec množství nenalezen - použiji defaultní hodnotu 1")
            df['mnozstvi'] = 1
        else:
            df['mnozstvi'] = df[mnozstvi_col].apply(parse_quantity)

        df['nazev'] = df[nazev_col]

        # Vytváření klíčů (stejně jako LABARA)
        print(f"\n🔑 VYTVÁŘENÍ KLÍČŮ PRO DESCON:")
        print("-" * 40)

        successful_keys = 0
        failed_keys = 0

        for idx, row in df.iterrows():
            nazev = str(row['nazev'])
            material, thickness = extract_material_thickness_from_name(nazev)
            key = create_robust_key(material, thickness)

            if key:
                successful_keys += 1
                df.at[idx, 'robust_key'] = key
                print(f"✅ [{idx+1:2d}] '{nazev[:40]}...' → {key}")
            else:
                failed_keys += 1
                df.at[idx, 'robust_key'] = None
                print(f"❌ [{idx+1:2d}] '{nazev[:40]}...' → NELZE VYTVOŘIT")

        # Vytváření klíčů pro katalog
        catalog_df['robust_key'] = catalog_df['DESCRIPTION'].apply(create_catalog_key_universal)

        print(f"\n📊 STATISTIKY KLÍČŮ:")
        print(f"   ✅ Úspěšně vytvořeno z objednávky: {successful_keys}/{len(df)} ({successful_keys/len(df)*100:.1f}%)")
        print(f"   📚 Klíčů v katalogu: {len(catalog_df[catalog_df['robust_key'].notna()])}")

        # Mapování s katalogem
        print(f"\n🔗 MAPOVÁNÍ S KATALOGEM:")
        final_df = pd.merge(
            df,
            catalog_df[['PART_NO', 'DESCRIPTION', 'robust_key']],
            on='robust_key',
            how='left'
        )

        matched_items = final_df[final_df['PART_NO'].notna()]
        unmatched_items = final_df[final_df['PART_NO'].isnull()]

        print(f"   ✅ Úspěšně namapováno: {len(matched_items)}")
        print(f"   ❌ Nenalezeno v katalogu: {len(unmatched_items)}")

        if not unmatched_items.empty:
            print(f"\n⚠️ NENALEZENÉ DESCON POLOŽKY:")
            for idx, row in unmatched_items.head(5).iterrows():
                nazev = row['nazev']
                key = row['robust_key']
                print(f"   • '{nazev[:50]}...' (klíč: {key if key else 'ŽÁDNÝ'})")

        # Parsování rozměrů
        print(f"\n📏 PARSOVÁNÍ ROZMĚRŮ:")
        for idx, row in final_df.iterrows():
            w, l = parse_dimensions(row['nazev'])
            final_df.at[idx, 'W'] = w
            final_df.at[idx, 'L'] = l

        # Vytvoření výstupu
        current_date = datetime.now().strftime('%d.%m.%Y')
        po_number = 'DESCON_' + datetime.now().strftime('%Y%m%d')

        output_df = pd.DataFrame({
            'CONTRACT': 'CZ21',
            'CUSTOMER_NO': '25555308',  # Pevný zákazník pro DESCON
            'CATALOG_NO': final_df['PART_NO'],
            'QTY': final_df['mnozstvi'],
            'DATED': current_date,
            'PRICE': 0,
            'CURRENCY_CODE': 'CZK',
            'CUSTOMER_PO_NO': po_number,
            'W': final_df['W'],
            'L': final_df['L']
        })

        # Filtrování platných dat
        before_count = len(output_df)
        output_df.dropna(subset=['CATALOG_NO'], inplace=True)
        after_count = len(output_df)

        print(f"\n📊 FINÁLNÍ STATISTIKY DESCON:")
        print(f"   📥 Vstupních řádků: {len(df)}")
        print(f"   🔑 Úspěšně vytvořených klíčů: {successful_keys}")
        print(f"   🔗 Namapováno s katalogem: {len(matched_items)}")
        print(f"   📤 Finálních platných položek: {after_count}")
        print(f"   📈 Celková úspěšnost: {after_count/len(df)*100:.1f}%")

        # KRITICKÁ KONTROLA
        if after_count == 0:
            print(f"\n❌ KRITICKÝ PROBLÉM: ŽÁDNÁ PLATNÁ DESCON DATA!")
            print(f"🔍 DIAGNOSTIKA:")
            print(f"   • Vstupních řádků: {len(df)}")
            print(f"   • Vytvořených klíčů: {successful_keys}")
            print(f"   • Namapovaných s katalogem: {len(matched_items)}")
            print(f"   • Řádků před filtrováním: {before_count}")
            print(f"   • Řádků po filtrování: {after_count}")

            if successful_keys == 0:
                print(f"   💡 PROBLÉM: Žádné DESCON klíče se nevytvořily!")
                print(f"   💡 ŘEŠENÍ: Zkontrolujte formát názvů produktů")
                print(f"   💡 PŘÍKLAD: '5083 litá přířez 10,00 - 80 x 90'")
            elif len(matched_items) == 0:
                print(f"   💡 PROBLÉM: DESCON klíče se vytvořily, ale neshodují se s katalogem!")
                print(f"   💡 ŘEŠENÍ: Materiály nejsou v katalogu nebo špatný formát klíčů")
            else:
                print(f"   💡 PROBLÉM: DESCON data se namapovala, ale ztratila se při filtrování!")

        return output_df

    except Exception as e:
        print(f"❌ KRITICKÁ CHYBA DESCON: {e}")
        traceback.print_exc()
        raise

def save_output(output_df, file_type):
    """Uloží výstupní data do CSV souboru."""
    if output_df.empty:
        print("⚠️ Žádná data k uložení!")
        return None

    # Vytvoření výstupního adresáře
    output_dir = os.path.join(os.path.expanduser("~"), "Documents", "Migrace_Vystupy")
    os.makedirs(output_dir, exist_ok=True)

    # Název souboru
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{file_type}_vystup_{timestamp}.csv"
    filepath = os.path.join(output_dir, filename)

    # Uložení
    output_df.to_csv(filepath, index=False, sep=';', encoding='utf-8-sig')
    print(f"✅ Výstup uložen: {filepath}")
    return filepath

class MigraceApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Migrace Objednávek v8.0 - KOMPLETNĚ OPRAVENO")
        self.root.geometry("600x500")

        # Hlavní frame
        main_frame = ttk.Frame(root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Nadpis
        title_label = ttk.Label(main_frame, text="🔧 Migrace Objednávek v8.0", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        subtitle_label = ttk.Label(main_frame, text="KOMPLETNĚ OPRAVENÁ VERZE - VŠECHNY TŘI TYPY FUNGUJÍ", font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

        # Výběr typu objednávky
        ttk.Label(main_frame, text="Typ objednávky:", font=("Arial", 12, "bold")).grid(row=2, column=0, sticky=tk.W, pady=(0, 10))

        self.order_type = tk.StringVar(value="LABARA")

        ttk.Radiobutton(main_frame, text="📄 LABARA (CSV/Excel)", variable=self.order_type, value="LABARA").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Radiobutton(main_frame, text="📊 IMPA (Excel)", variable=self.order_type, value="IMPA").grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Radiobutton(main_frame, text="📋 DESCON (Excel)", variable=self.order_type, value="DESCON").grid(row=5, column=0, sticky=tk.W, pady=2)

        # Výběr souboru
        ttk.Label(main_frame, text="Soubor objednávky:", font=("Arial", 12, "bold")).grid(row=6, column=0, sticky=tk.W, pady=(20, 10))

        self.file_path = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.file_path, width=50).grid(row=7, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(main_frame, text="Procházet...", command=self.browse_file).grid(row=7, column=1, padx=(10, 0), pady=2)

        # Vstupní pole pro LABARA a IMPA
        self.input_frame = ttk.LabelFrame(main_frame, text="Vstupní údaje", padding="10")
        self.input_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))

        # Datum dodání
        ttk.Label(self.input_frame, text="Datum dodání (DD.MM.RRRR):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.dated_input = tk.StringVar(value=datetime.now().strftime("%d.%m.%Y"))
        ttk.Entry(self.input_frame, textvariable=self.dated_input, width=20).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # Číslo zákazníka
        ttk.Label(self.input_frame, text="Číslo zákazníka:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.customer_no_input = tk.StringVar(value="504398")
        ttk.Entry(self.input_frame, textvariable=self.customer_no_input, width=20).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # PO číslo (jen pro IMPA)
        ttk.Label(self.input_frame, text="PO číslo (jen IMPA):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.po_input = tk.StringVar()
        ttk.Entry(self.input_frame, textvariable=self.po_input, width=20).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # Tlačítko zpracování
        process_button = ttk.Button(main_frame, text="🚀 ZPRACOVAT OBJEDNÁVKU", command=self.process_order, style="Accent.TButton")
        process_button.grid(row=9, column=0, columnspan=2, pady=(30, 10))

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=10, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # Status label
        self.status_label = ttk.Label(main_frame, text="Připraveno k zpracování", foreground="green")
        self.status_label.grid(row=11, column=0, columnspan=2, pady=(0, 10))

        # Info text
        info_text = """
🔧 VERZE 8.0 - KOMPLETNĚ OPRAVENO:
✅ LABARA - robustní mapování + diagnostika
✅ IMPA - kompletně přepracováno + diagnostika
✅ DESCON - opraveno + diagnostika
✅ Maximální diagnostika pro všechny typy
✅ Přesná identifikace problémů
✅ Návrhy řešení pro každý problém
        """

        info_label = ttk.Label(main_frame, text=info_text, font=("Arial", 9), foreground="blue")
        info_label.grid(row=12, column=0, columnspan=2, pady=(10, 0))

        # Konfigurace grid
        main_frame.columnconfigure(0, weight=1)
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)

    def browse_file(self):
        """Otevře dialog pro výběr souboru."""
        filetypes = [
            ("Všechny podporované", "*.csv;*.xlsx;*.xls"),
            ("CSV soubory", "*.csv"),
            ("Excel soubory", "*.xlsx;*.xls"),
            ("Všechny soubory", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="Vyberte soubor objednávky",
            filetypes=filetypes
        )

        if filename:
            self.file_path.set(filename)

    def process_order(self):
        """Zpracuje objednávku podle vybraného typu."""
        if not self.file_path.get():
            messagebox.showerror("Chyba", "Vyberte soubor objednávky!")
            return

        if not os.path.exists(self.file_path.get()):
            messagebox.showerror("Chyba", "Vybraný soubor neexistuje!")
            return

        # Validace vstupních údajů
        order_type = self.order_type.get()

        if order_type in ["LABARA", "IMPA"]:
            if not self.dated_input.get():
                messagebox.showerror("Chyba", "Zadejte datum dodání!")
                return

            if not self.customer_no_input.get():
                messagebox.showerror("Chyba", "Zadejte číslo zákazníka!")
                return

        if order_type == "IMPA" and not self.po_input.get():
            messagebox.showerror("Chyba", "Zadejte PO číslo pro IMPA!")
            return

        # Spuštění zpracování
        self.progress.start()
        self.status_label.config(text="Zpracovávám...", foreground="orange")
        self.root.update()

        try:
            print(f"\n{'='*80}")
            print(f"🚀 SPOUŠTÍM ZPRACOVÁNÍ: {order_type}")
            print(f"📁 Soubor: {os.path.basename(self.file_path.get())}")
            print(f"{'='*80}")

            # Zpracování podle typu
            if order_type == "LABARA":
                output_df = process_labara_order_fixed(
                    self.file_path.get(),
                    self.dated_input.get(),
                    self.customer_no_input.get()
                )
            elif order_type == "IMPA":
                output_df = process_impa_order_fixed(
                    self.file_path.get(),
                    self.dated_input.get(),
                    self.customer_no_input.get(),
                    self.po_input.get()
                )
            elif order_type == "DESCON":
                output_df = process_descon_order_fixed(self.file_path.get())

            # Uložení výsledku
            if not output_df.empty:
                output_file = save_output(output_df, order_type)
                self.status_label.config(text=f"✅ Úspěšně zpracováno! Uloženo: {os.path.basename(output_file)}", foreground="green")
                messagebox.showinfo("Úspěch", f"Objednávka byla úspěšně zpracována!\n\nVýstup: {output_file}\nPočet položek: {len(output_df)}")
            else:
                self.status_label.config(text="⚠️ Žádná platná data k uložení!", foreground="red")
                messagebox.showwarning("Varování", "Po zpracování nebyla nalezena žádná platná data k uložení!\n\nZkontrolujte konzoli pro detailní diagnostiku.")

        except Exception as e:
            self.status_label.config(text=f"❌ Chyba: {str(e)}", foreground="red")
            messagebox.showerror("Chyba", f"Chyba při zpracování:\n{str(e)}")
            print(f"❌ KRITICKÁ CHYBA: {e}")
            traceback.print_exc()

        finally:
            self.progress.stop()

def main():
    """Hlavní funkce aplikace."""
    print("🚀 MIGRACE OBJEDNÁVEK v8.0 - KOMPLETNĚ OPRAVENÁ VERZE")
    print("=" * 60)
    print("✅ LABARA - robustní mapování + diagnostika")
    print("✅ IMPA - kompletně přepracováno + diagnostika")
    print("✅ DESCON - opraveno + diagnostika")
    print("✅ Maximální diagnostika pro všechny typy")
    print("=" * 60)

    root = tk.Tk()
    app = MigraceApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
