@echo off
title Migrace Objednavek - VYLEPŠENÁ DIAGNOSTIKA - Build EXE
echo ================================================================
echo   MIGRACE OBJEDNAVEK v7.1 - VYLEPŠENÁ DIAGNOSTIKA
echo   Vytvoreni Windows 64-bit EXE s detailnimi logy
echo ================================================================
echo.
echo 🔍 NOVÉ FUNKCE V TÉTO VERZI:
echo    ✅ Podpora CSV i Excel pro LABARA
echo    ✅ Detailni diagnostika kazde polozky
echo    ✅ Statistiky uspesnosti mapovani
echo    ✅ Zobrazeni chybejicich polozek
echo    ✅ Hledani podobnych polozek v katalogu
echo    ✅ Flexibilni detekce sloupcu
echo    ✅ Lepsi zpracovani mnozstvi a rozmeru
echo.

echo [1/4] Kontrola Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python neni nainstalovany!
    pause
    exit /b 1
)
echo ✅ Python nalezen

echo [2/4] Kontrola zavislosti...
pip install pandas openpyxl --quiet
echo ✅ Zavislosti pripraveny

echo [3/4] Vytvarim EXE s vylepšenou diagnostikou...
echo    🔍 Balim aplikaci s detailnimi logy...
pyinstaller --onefile --windowed ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import tkinter ^
    --hidden-import datetime ^
    --hidden-import traceback ^
    --hidden-import winreg ^
    --name "Migrace_Objednavek_Enhanced" ^
    --icon=NONE ^
    migrace_standalone_final.py

if errorlevel 1 (
    echo ❌ Build selhal!
    pause
    exit /b 1
)

echo [4/4] Finalizace...
if exist "dist\Migrace_Objednavek_Enhanced.exe" (
    copy "dist\Migrace_Objednavek_Enhanced.exe" ".\Migrace_Objednavek_Enhanced.exe" >nul
    echo.
    echo ================================================================
    echo   🎉 SUCCESS! VYLEPŠENÝ EXE VYTVOREN!
    echo ================================================================
    echo.
    echo 📁 Soubor: Migrace_Objednavek_Enhanced.exe
    for %%A in ("Migrace_Objednavek_Enhanced.exe") do echo 📊 Velikost: %%~zA bytes
    echo.
    echo 🔍 NOVÉ DIAGNOSTICKÉ FUNKCE:
    echo    • Detailni logy pro kazdou polozku
    echo    • Statistiky uspesnosti mapovani
    echo    • Seznam chybejicich polozek
    echo    • Navrhy podobnych polozek
    echo    • Podpora CSV i Excel pro LABARA
    echo    • Flexibilni detekce sloupcu
    echo.
    echo ✅ NYNÍ UVIDÍTE PŘESNĚ, KTERÉ POLOŽKY CHYBÍ A PROČ!
    echo.
) else (
    echo ❌ EXE soubor nebyl vytvoren!
    pause
    exit /b 1
)

echo Chcete spustit test? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo Spoustim vylepšenou aplikaci...
    start Migrace_Objednavek_Enhanced.exe
)

pause
