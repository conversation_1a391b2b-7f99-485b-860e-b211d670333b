@echo off
title PyInstaller EXE Builder
echo ================================================
echo   PYINSTALLER WINDOWS EXE BUILDER
echo   Vytvoreni nativniho Windows EXE
echo ================================================
echo.

echo [1/4] Kontrola Python...
python --version || goto :nopython

echo [2/4] Instalace PyInstaller...
pip install pyinstaller

echo [3/4] Build EXE (muze trvat 5 minut)...
pyinstaller --onefile --windowed ^
    --add-data "ALDE_katalog_polozek.xlsx;." ^
    --add-data "IMPA_Test_Data;IMPA_Test_Data" ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import tkinter ^
    --name "Migrace_Objednavek" ^
    main_fixed.py

echo [4/4] Finalizace...
if exist "dist\Migrace_Objednavek.exe" (
    copy "dist\Migrace_Objednavek.exe" ".\Migrace_Objednavek.exe"
    echo [SUCCESS] Windows EXE vytvoren!
    echo Velikost:
    for %%A in ("Migrace_Objednavek.exe") do echo %%~zA bytes
    echo.
    echo Nyni muzete pouzit:
    echo - Migrace_Objednavek.exe (primo)
    echo - SPUSTIT_APLIKACI.bat (univerzalni)
    echo - START_SIMPLE.bat (jednoduchy)
) else (
    echo [ERROR] Build selhal!
    echo Zkontrolujte chyby vyse
)

goto :end

:nopython
echo [ERROR] Python neni nainstalovany!
echo.
echo RESENI:
echo 1. Jdete na https://python.org/downloads/
echo 2. Stahnete Python 3.8+
echo 3. Pri instalaci zasktrnete "Add Python to PATH"
echo 4. Restartujete Command Prompt
echo 5. Spustite tento script znovu

:end
pause
