@echo off
title Migrace Objednavek - OPRAVA LABARA - Build EXE
echo ================================================================
echo   MIGRACE OBJEDNAVEK v7.1 - OPRAVA LABARA PROBLEMU
echo   Vytvoreni opraveneho Windows 64-bit EXE
echo ================================================================
echo.
echo 🔧 OPRAVY V TETO VERZI:
echo    ✅ Robustni mapovani pro LABARA (misto primych nazvu)
echo    ✅ Flexibilni detekce sloupcu
echo    ✅ Lepsi error handling a diagnostika
echo    ✅ Vylepšene parsovani rozmeru
echo    ✅ Detailni logy pro debugging
echo.

echo [1/4] Kontrola Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python neni nainstalovany!
    pause
    exit /b 1
)
echo ✅ Python nalezen

echo [2/4] Kontrola zavislosti...
pip install pandas openpyxl --quiet
echo ✅ Zavislosti pripraveny

echo [3/4] Vytvarim opraveny Windows EXE...
echo    🔧 Balim aplikaci s opravenou LABARA funkci...
pyinstaller --onefile --windowed ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import tkinter ^
    --hidden-import datetime ^
    --hidden-import traceback ^
    --hidden-import winreg ^
    --name "Migrace_Objednavek_Fixed" ^
    --icon=NONE ^
    migrace_standalone_final.py

if errorlevel 1 (
    echo ❌ Build selhal!
    pause
    exit /b 1
)

echo [4/4] Finalizace...
if exist "dist\Migrace_Objednavek_Fixed.exe" (
    copy "dist\Migrace_Objednavek_Fixed.exe" ".\Migrace_Objednavek_Fixed.exe" >nul
    echo.
    echo ================================================================
    echo   🎉 SUCCESS! OPRAVENY EXE VYTVOREN!
    echo ================================================================
    echo.
    echo 📁 Soubor: Migrace_Objednavek_Fixed.exe
    for %%A in ("Migrace_Objednavek_Fixed.exe") do echo 📊 Velikost: %%~zA bytes
    echo.
    echo 🔧 OPRAVY LABARA:
    echo    • Robustni mapovani pomoci klicu (material-tloustka)
    echo    • Automaticka detekce sloupcu
    echo    • Lepsi error handling
    echo    • Detailni diagnosticke vypisy
    echo    • Flexibilni parsovani rozmeru
    echo.
    echo ✅ TESTUJTE S LABARA SOUBORY!
    echo.
) else (
    echo ❌ EXE soubor nebyl vytvoren!
    pause
    exit /b 1
)

echo Chcete spustit test? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo Spoustim opravenou aplikaci...
    start Migrace_Objednavek_Fixed.exe
)

pause
