@echo off
title Migrace Objednavek - Final Standalone EXE Builder
echo ================================================================
echo   MIGRACE OBJEDNAVEK v7.1 - FINAL STANDALONE EXE BUILDER
echo   Vytvoreni kompletniho Windows 64-bit EXE
echo ================================================================
echo.
echo 🎯 VYTVARIM KOMPLETNI STANDALONE EXE:
echo    ✅ Vestaveny katalog (508 polozek)
echo    ✅ Zadne externi soubory potreba
echo    ✅ Optimalizovano pro Windows 64-bit
echo    ✅ Jednoduche spusteni - jen kliknout a pouzivat!
echo.

echo [1/5] Kontrola Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python neni nainstalovany!
    echo.
    echo RESENI:
    echo 1. Jdete na https://python.org/downloads/
    echo 2. Stahnete Python 3.8+
    echo 3. Pri instalaci zaškrtnete "Add Python to PATH"
    echo 4. Restartujete Command Prompt
    echo 5. Spustite tento script znovu
    pause
    exit /b 1
)
echo ✅ Python nalezen

echo [2/5] Instalace PyInstaller...
pip install pyinstaller --quiet
if errorlevel 1 (
    echo ❌ Chyba pri instalaci PyInstaller
    pause
    exit /b 1
)
echo ✅ PyInstaller nainstalovany

echo [3/5] Kontrola zavislosti...
pip install pandas openpyxl --quiet
if errorlevel 1 (
    echo ❌ Chyba pri instalaci zavislosti
    pause
    exit /b 1
)
echo ✅ Zavislosti pripraveny

echo [4/5] Vytvarim Windows EXE (muze trvat 3-5 minut)...
echo    📦 Balim aplikaci + vestaveny katalog...
pyinstaller --onefile --windowed ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import tkinter ^
    --hidden-import datetime ^
    --hidden-import traceback ^
    --hidden-import winreg ^
    --name "Migrace_Objednavek_Final" ^
    --icon=NONE ^
    --add-data "migrace_standalone_final.py;." ^
    migrace_standalone_final.py

if errorlevel 1 (
    echo ❌ Build selhal!
    echo Zkontrolujte chyby vyse
    pause
    exit /b 1
)

echo [5/5] Finalizace...
if exist "dist\Migrace_Objednavek_Final.exe" (
    copy "dist\Migrace_Objednavek_Final.exe" ".\Migrace_Objednavek_Final.exe" >nul
    echo.
    echo ================================================================
    echo   🎉 SUCCESS! STANDALONE EXE VYTVOREN!
    echo ================================================================
    echo.
    echo 📁 Soubor: Migrace_Objednavek_Final.exe
    for %%A in ("Migrace_Objednavek_Final.exe") do echo 📊 Velikost: %%~zA bytes
    echo.
    echo ✅ HOTOVO! Nyni muzete:
    echo    1️⃣ Spustit: Migrace_Objednavek_Final.exe
    echo    2️⃣ Zkopirovat EXE kamkoliv (je kompletne samostatny)
    echo    3️⃣ Spustit na jakemkoliv Windows PC (bez instalace)
    echo.
    echo 🎯 OBSAHUJE:
    echo    • Kompletni aplikaci pro migraci objednavek
    echo    • Vestaveny ALDE katalog (508 polozek)
    echo    • Podporu pro LABARA, IMPA, DESCON formaty
    echo    • Automaticke ukladani do Documents/Migrace_Vystupy/
    echo.
    echo 💡 POUZITI:
    echo    Jednoduše spustte EXE a vyberte vstupni soubory!
    echo.
) else (
    echo ❌ EXE soubor nebyl vytvoren!
    echo Zkontrolujte chyby vyse
    pause
    exit /b 1
)

echo Chcete otevrit slozku s EXE? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    explorer .
)

echo.
echo Stisknete libovolnou klavesu pro ukonceni...
pause >nul
