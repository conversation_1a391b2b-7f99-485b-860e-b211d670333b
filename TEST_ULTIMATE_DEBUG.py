#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST ULTIMATE DEBUG FUNKCE
==========================
Testuje, jestli se Ultimate Debug změny skutečně uložily
"""

import pandas as pd
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_standalone_final import process_labara_order, process_impa_order
    print("✅ Modul úspěšně načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def test_ultimate_debug():
    """Test Ultimate Debug funkce."""
    print("🔍 TEST ULTIMATE DEBUG FUNKCE")
    print("=" * 50)
    
    # Test 1: LABARA s prázdnými daty (mělo by spustit diagnostiku)
    print("\n📋 TEST 1: LABARA S PRÁZDNÝMI DATY")
    print("-" * 30)
    
    # Vyt<PERSON><PERSON><PERSON><PERSON> soubor s neplatnými daty
    test_data = {
        'nazev': ['Neplatný název bez materiálu', 'Další neplatný název', 'Třetí špatný název'],
        'mnozstvi': [1, 2, 3]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_ultimate_debug_labara.csv'
    df.to_csv(test_file, index=False, sep=';', encoding='windows-1250')
    print(f"✅ Vytvořen testovací LABARA soubor: {test_file}")
    
    try:
        print(f"\n🚀 SPOUŠTÍM LABARA ZPRACOVÁNÍ (mělo by ukázat diagnostiku):")
        print("-" * 50)
        
        result_df = process_labara_order(
            order_path=test_file,
            dated_input="01.08.2025", 
            customer_no_input="504398"
        )
        
        print(f"\n📊 VÝSLEDEK LABARA:")
        print(f"   Zpracováno: {len(result_df)} položek")
        
        if len(result_df) == 0:
            print("✅ SPRÁVNĚ: Žádné platné položky (očekáváno)")
            print("✅ Pokud jsi viděl 'KRITICKÝ PROBLÉM' výše, diagnostika funguje!")
        else:
            print("❌ NEOČEKÁVÁNO: Nějaké položky se zpracovaly")
            
    except Exception as e:
        print(f"❌ CHYBA: {e}")
    
    finally:
        # Úklid
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 Smazán testovací soubor: {test_file}")
    
    # Test 2: IMPA s prázdnými daty
    print(f"\n📋 TEST 2: IMPA S PRÁZDNÝMI DATY")
    print("-" * 30)
    
    # Vytvoříme IMPA soubor s neplatnými daty
    test_data = {
        'Material': ['NeznámýMat1', 'NeznámýMat2', 'NeznámýMat3'],
        'Hrubka': [999, 888, 777],  # Neexistující hrubky
        'Sirka': [100, 200, 150],
        'Dlzka': [200, 400, 300],
        'Pocet kusov': [1, 2, 3]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_ultimate_debug_impa.xlsx'
    df.to_excel(test_file, index=False)
    print(f"✅ Vytvořen testovací IMPA soubor: {test_file}")
    
    try:
        print(f"\n🚀 SPOUŠTÍM IMPA ZPRACOVÁNÍ (mělo by ukázat diagnostiku):")
        print("-" * 50)
        
        result_df = process_impa_order(
            order_path=test_file,
            dated_input="01.08.2025", 
            customer_no_input="504398",
            po_input="TEST_DEBUG"
        )
        
        print(f"\n📊 VÝSLEDEK IMPA:")
        print(f"   Zpracováno: {len(result_df)} položek")
        
        if len(result_df) == 0:
            print("✅ SPRÁVNĚ: Žádné platné položky (očekáváno)")
            print("✅ Pokud jsi viděl 'KRITICKÝ PROBLÉM' výše, diagnostika funguje!")
        else:
            print("❌ NEOČEKÁVÁNO: Nějaké položky se zpracovaly")
            
    except Exception as e:
        print(f"❌ CHYBA: {e}")
    
    finally:
        # Úklid
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 Smazán testovací soubor: {test_file}")
    
    print(f"\n🎯 SHRNUTÍ TESTU:")
    print("=" * 50)
    print("Pokud jsi viděl výše zprávy typu:")
    print("❌ KRITICKÝ PROBLÉM: ŽÁDNÁ PLATNÁ DATA!")
    print("🔍 DIAGNOSTIKA:")
    print("   • Vstupních řádků: X")
    print("   • Vytvořených klíčů: Y")
    print("   💡 PROBLÉM: [důvod]")
    print("   💡 ŘEŠENÍ: [kroky]")
    print("")
    print("✅ ULTIMATE DEBUG FUNGUJE!")
    print("❌ Pokud jsi tyto zprávy neviděl, diagnostika nefunguje")

if __name__ == "__main__":
    test_ultimate_debug()
    input("\nStiskněte Enter pro ukončení...")
