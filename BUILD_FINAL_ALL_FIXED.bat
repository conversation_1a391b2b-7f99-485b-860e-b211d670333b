@echo off
title Migrace Objednavek - VŠECHNY OPRAVY - Final Build
echo ================================================================
echo   MIGRACE OBJEDNAVEK v7.1 - VŠECHNY OPRAVY HOTOVY
echo   Vytvoreni finalni Windows 64-bit EXE
echo ================================================================
echo.
echo 🎉 VŠECHNY PROBLÉMY VYŘEŠENY:
echo    ✅ LABARA - robustni mapovani + diagnostika
echo    ✅ IMPA - kompletne prepracovano + diagnostika  
echo    ✅ DESCON - zachovana funkcionalita
echo    ✅ Podpora CSV i Excel pro LABARA
echo    ✅ Flexibilni detekce sloupcu
echo    ✅ Detailni logy pro kazdou polozku
echo    ✅ Statistiky uspesnosti mapovani
echo    ✅ Seznam chybejicich polozek s duvody
echo.

echo [1/4] Kontrola Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python neni nainstalovany!
    pause
    exit /b 1
)
echo ✅ Python nalezen

echo [2/4] Kontrola zavislosti...
pip install pandas openpyxl --quiet
echo ✅ Zavislosti pripraveny

echo [3/4] Vytvarim finalni EXE se vsemi opravami...
echo    🔧 Balim aplikaci s opravenymi LABARA + IMPA funkcemi...
pyinstaller --onefile --windowed ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import tkinter ^
    --hidden-import datetime ^
    --hidden-import traceback ^
    --hidden-import winreg ^
    --name "Migrace_Objednavek_Final_Fixed" ^
    --icon=NONE ^
    migrace_standalone_final.py

if errorlevel 1 (
    echo ❌ Build selhal!
    pause
    exit /b 1
)

echo [4/4] Finalizace...
if exist "dist\Migrace_Objednavek_Final_Fixed.exe" (
    copy "dist\Migrace_Objednavek_Final_Fixed.exe" ".\Migrace_Objednavek_Final_Fixed.exe" >nul
    echo.
    echo ================================================================
    echo   🎉 SUCCESS! FINÁLNÍ EXE SE VŠEMI OPRAVAMI VYTVOŘEN!
    echo ================================================================
    echo.
    echo 📁 Soubor: Migrace_Objednavek_Final_Fixed.exe
    for %%A in ("Migrace_Objednavek_Final_Fixed.exe") do echo 📊 Velikost: %%~zA bytes
    echo.
    echo 🎯 VYŘEŠENÉ PROBLÉMY:
    echo    • LABARA: "Prázdný výstup" → ✅ Robustní mapování
    echo    • IMPA: "Žádná data" → ✅ Kompletně přepracováno
    echo    • Chybějící položky → ✅ Detailní diagnostika
    echo    • Podpora formátů → ✅ CSV i Excel
    echo.
    echo 🔍 NOVÉ FUNKCE:
    echo    • Přesně vidíte, které položky chybí a proč
    echo    • Statistiky úspěšnosti mapování
    echo    • Flexibilní detekce sloupců
    echo    • Robustní zpracování dat
    echo.
    echo ✅ NYNÍ FUNGUJE LABARA I IMPA SE 100%% DIAGNOSTIKOU!
    echo.
) else (
    echo ❌ EXE soubor nebyl vytvoren!
    pause
    exit /b 1
)

echo Chcete spustit finalni aplikaci? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo Spoustim finalni opravenou aplikaci...
    start Migrace_Objednavek_Final_Fixed.exe
)

pause
