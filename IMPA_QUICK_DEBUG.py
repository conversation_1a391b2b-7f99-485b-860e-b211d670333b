#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMPA RYCHLÁ DIAGNOSTIKA
======================
Rychlý debug pro zjištění, proč IMPA nevrací data
"""

import pandas as pd
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

def quick_impa_debug():
    """Rychlá diagnostika IMPA problému."""
    print("🔍 IMPA RYCHLÁ DIAGNOSTIKA")
    print("=" * 50)
    
    # Požádáme o cestu k IMPA souboru
    print("\n📁 Zadejte cestu k vašemu IMPA souboru:")
    print("   (nebo stiskněte Enter pro vytvoření testovacího)")
    
    file_path = input("Cesta: ").strip().strip('"')
    
    if not file_path:
        # Vytvoříme testovací soubor
        print("\n📝 Vytvářím testovací IMPA soubor...")
        test_data = {
            'Material': ['5083', '6061', '5754'],
            'Hrubka': [10.0, 25.0, 30.0],
            'Sirka': [100, 200, 150],
            'Dlzka': [200, 400, 300],
            'Pocet kusov': [2, 3, 1]
        }
        df = pd.DataFrame(test_data)
        file_path = 'quick_test_impa.xlsx'
        df.to_excel(file_path, index=False)
        print(f"✅ Vytvořen: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ Soubor neexistuje: {file_path}")
        return
    
    try:
        print(f"\n📂 NAČÍTÁNÍ SOUBORU: {os.path.basename(file_path)}")
        print("-" * 30)
        
        # Načteme soubor
        if file_path.lower().endswith('.csv'):
            df = pd.read_csv(file_path, sep=';', encoding='windows-1250')
        else:
            df = pd.read_excel(file_path)
        
        print(f"✅ Úspěšně načteno: {len(df)} řádků, {len(df.columns)} sloupců")
        print(f"📋 Sloupce: {list(df.columns)}")
        
        print(f"\n📊 UKÁZKA DAT:")
        print(df.head().to_string(index=False))
        
        # Zkusíme najít klíčové sloupce
        print(f"\n🔍 HLEDÁNÍ KLÍČOVÝCH SLOUPCŮ:")
        
        material_col = None
        hrubka_col = None
        
        for col in df.columns:
            col_lower = str(col).lower()
            if 'material' in col_lower or 'materiál' in col_lower:
                material_col = col
                print(f"✅ Materiál nalezen: '{col}'")
            elif 'hrubka' in col_lower or 'thickness' in col_lower or 'thick' in col_lower:
                hrubka_col = col
                print(f"✅ Hrubka nalezena: '{col}'")
        
        if not material_col:
            print("❌ Sloupec materiálu nenalezen!")
            print("💡 Hledám podobné sloupce...")
            for col in df.columns:
                print(f"   - '{col}'")
        
        if not hrubka_col:
            print("❌ Sloupec hrubky nenalezen!")
            print("💡 Hledám podobné sloupce...")
            for col in df.columns:
                print(f"   - '{col}'")
        
        if material_col and hrubka_col:
            print(f"\n🔑 TESTOVÁNÍ VYTVÁŘENÍ KLÍČŮ:")
            print("-" * 30)
            
            for idx, row in df.head(5).iterrows():
                material = row[material_col]
                hrubka = row[hrubka_col]
                
                # Normalizace materiálu
                material_norm = str(material).strip()
                if material_norm in ["6061", "6061T651"]:
                    material_norm = "ENAW6061T651"
                elif material_norm in ["7075", "7075T651"]:
                    material_norm = "ENAW7075T651"
                elif material_norm == "5754":
                    material_norm = "ENAW5754H111"
                
                # Vytvoření klíče
                try:
                    hrubka_float = float(str(hrubka).replace(',', '.'))
                    key = f"{material_norm}-{hrubka_float:.2f}"
                    print(f"✅ [{idx+1}] '{material}' + '{hrubka}' → {key}")
                except:
                    print(f"❌ [{idx+1}] '{material}' + '{hrubka}' → CHYBA")
        
        # Zkusíme načíst katalog
        print(f"\n📚 TESTOVÁNÍ KATALOGU:")
        try:
            from migrace_standalone_final import get_embedded_catalog
            catalog_df = get_embedded_catalog()
            print(f"✅ Katalog načten: {len(catalog_df)} položek")
            
            # Ukázka katalogu
            print(f"\n📋 UKÁZKA KATALOGU (5083 položky):")
            catalog_5083 = catalog_df[catalog_df['DESCRIPTION'].str.contains('5083', na=False)]
            for idx, row in catalog_5083.head(3).iterrows():
                print(f"   {row['PART_NO']} - {row['DESCRIPTION']}")
                
        except Exception as e:
            print(f"❌ Chyba při načítání katalogu: {e}")
        
    except Exception as e:
        print(f"❌ CHYBA: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Úklid
        if file_path == 'quick_test_impa.xlsx' and os.path.exists(file_path):
            os.remove(file_path)
            print(f"\n🧹 Smazán testovací soubor")
    
    print(f"\n💡 DOPORUČENÍ:")
    print("1. Zkontrolujte názvy sloupců v IMPA souboru")
    print("2. Ujistěte se, že obsahuje 'Material' a 'Hrubka'")
    print("3. Zkontrolujte, že data nejsou prázdná")
    print("4. Spusťte aplikaci z Command Prompt pro zobrazení logů")

if __name__ == "__main__":
    quick_impa_debug()
    input("\nStiskněte Enter pro ukončení...")
