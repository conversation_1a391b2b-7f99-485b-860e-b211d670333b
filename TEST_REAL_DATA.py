#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST REÁLNÝCH DAT
================
Testuje zpracování reálných vstupních souborů
"""

import pandas as pd
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_final_all_fixed import process_labara_order_fixed, process_impa_order_fixed, process_descon_order_fixed
    print("✅ Modul úspěšně načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def test_real_labara():
    """Test reálných LABARA dat."""
    print("\n📄 TEST REÁLNÝCH LABARA DAT")
    print("-" * 40)
    
    labara_files = [
        "vstup/OV22503440.csv",
        "vstup/OV22503745.csv", 
        "vstup/OV22503910.csv"
    ]
    
    for file_path in labara_files:
        if os.path.exists(file_path):
            print(f"\n🔍 Testování: {os.path.basename(file_path)}")
            try:
                result_df = process_labara_order_fixed(
                    file_path,
                    "01.08.2025", 
                    "504398"
                )
                
                print(f"📊 VÝSLEDEK: {len(result_df)} položek")
                if len(result_df) > 0:
                    print("✅ LABARA FUNGUJE!")
                else:
                    print("❌ LABARA - žádné platné položky")
                    
            except Exception as e:
                print(f"❌ CHYBA: {e}")
        else:
            print(f"⚠️ Soubor neexistuje: {file_path}")

def test_real_impa():
    """Test reálných IMPA dat."""
    print("\n📊 TEST REÁLNÝCH IMPA DAT")
    print("-" * 40)
    
    impa_files = [
        "vstup/IMPA1.xlsx",
        "vstup/IMPA2.xlsx",
        "vstup/IMPA3.xlsx"
    ]
    
    for file_path in impa_files:
        if os.path.exists(file_path):
            print(f"\n🔍 Testování: {os.path.basename(file_path)}")
            try:
                result_df = process_impa_order_fixed(
                    file_path,
                    "01.08.2025", 
                    "504398",
                    "IMPA_REAL_TEST"
                )
                
                print(f"📊 VÝSLEDEK: {len(result_df)} položek")
                if len(result_df) > 0:
                    print("✅ IMPA FUNGUJE!")
                else:
                    print("❌ IMPA - žádné platné položky")
                    
            except Exception as e:
                print(f"❌ CHYBA: {e}")
        else:
            print(f"⚠️ Soubor neexistuje: {file_path}")

def test_real_descon():
    """Test reálných DESCON dat."""
    print("\n📋 TEST REÁLNÝCH DESCON DAT")
    print("-" * 40)
    
    descon_files = [
        "vstup/ALFUN - Q25_0085.xlsx",
        "vstup/ALFUN - Q25_0103.xlsx",
        "vstup/Q25_0087.xlsx"
    ]
    
    for file_path in descon_files:
        if os.path.exists(file_path):
            print(f"\n🔍 Testování: {os.path.basename(file_path)}")
            try:
                result_df = process_descon_order_fixed(file_path)
                
                print(f"📊 VÝSLEDEK: {len(result_df)} položek")
                if len(result_df) > 0:
                    print("✅ DESCON FUNGUJE!")
                else:
                    print("❌ DESCON - žádné platné položky")
                    
            except Exception as e:
                print(f"❌ CHYBA: {e}")
        else:
            print(f"⚠️ Soubor neexistuje: {file_path}")

def main():
    """Hlavní test reálných dat."""
    print("🧪 TEST VŠECH REÁLNÝCH VSTUPNÍCH DAT")
    print("=" * 60)
    
    # Test všech typů
    test_real_labara()
    test_real_impa() 
    test_real_descon()
    
    print(f"\n🎯 SHRNUTÍ:")
    print("=" * 60)
    print("Zkontrolujte výše uvedené výsledky pro každý typ:")
    print("✅ = typ funguje s reálnými daty")
    print("❌ = typ nefunguje s reálnými daty")

if __name__ == "__main__":
    main()
    input("\nStiskněte Enter pro ukončení...")
