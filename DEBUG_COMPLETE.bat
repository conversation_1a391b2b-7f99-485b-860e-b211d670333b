@echo off
title Migrace Debug - Complete Diagnostics
chcp 1250 >nul

echo ================================================
echo   MIGRACE OBJEDNAVEK - DEBUG DIAGNOSTICS
echo   Complete troubleshooting information
echo ================================================
echo.

echo [DEBUG] Systemove informace:
echo OS: %OS%
echo Architektura: %PROCESSOR_ARCHITECTURE%
echo Uzivatel: %USERNAME%
echo Datum: %DATE% %TIME%
echo Adresar: %CD%
echo.

echo [DEBUG] Kontrola souboru:
if exist "main_fixed.py" (echo [OK] main_fixed.py) else (echo [ERROR] main_fixed.py MISSING)
if exist "ALDE_katalog_polozek.xlsx" (echo [OK] katalog) else (echo [ERROR] katalog MISSING)
if exist "Migrace_Objednavek.exe" (echo [OK] Windows EXE) else (echo [INFO] Windows EXE not built yet)
if exist "python-portable\" (echo [OK] portable Python) else (echo [INFO] portable Python not setup)
echo.

echo [DEBUG] Python test:
python --version 2>nul && echo [OK] System Python dostupny || echo [INFO] System Python nedostupny

if exist "python-portable\python.exe" (
    echo [DEBUG] Portable Python test:
    "python-portable\python.exe" --version
)

echo.
echo [DEBUG] Knihovny test:
if exist "python-portable\python.exe" (
    "python-portable\python.exe" -c "import pandas; print('Pandas OK')" 2>nul || echo [WARNING] Pandas problem
    "python-portable\python.exe" -c "import openpyxl; print('OpenPyXL OK')" 2>nul || echo [WARNING] OpenPyXL problem
    "python-portable\python.exe" -c "import tkinter; print('Tkinter OK')" 2>nul || echo [WARNING] Tkinter problem
) else (
    python -c "import pandas; print('Pandas OK')" 2>nul || echo [WARNING] Pandas problem
    python -c "import openpyxl; print('OpenPyXL OK')" 2>nul || echo [WARNING] OpenPyXL problem
    python -c "import tkinter; print('Tkinter OK')" 2>nul || echo [WARNING] Tkinter problem
)

echo.
echo [DEBUG] Pokus o spusteni s debug vystupem:
echo ========================================

if exist "Migrace_Objednavek.exe" (
    echo Testuji Windows EXE...
    "Migrace_Objednavek.exe"
) else if exist "python-portable\python.exe" (
    echo Testuji portable Python...
    "python-portable\python.exe" -u main_fixed.py
) else (
    echo Testuji system Python...
    python -u main_fixed.py
)

echo.
echo [DEBUG] Test skoncen
if errorlevel 1 (
    echo Program skoncil s chybou: %errorlevel%
) else (
    echo Program skoncil uspesne
)

echo.
echo Pro dalsi pomoc:
echo 1. Zkontrolujte chyby vyse
echo 2. Spustte SPUSTIT_APLIKACI.bat pro auto-setup
echo 3. Kontaktujte IT podporu s temito informacemi

pause
