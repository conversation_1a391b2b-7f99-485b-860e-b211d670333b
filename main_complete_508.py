#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MIGRACE OBJEDNAVEK v7.1 - KOMPLETNÍ STANDALONE VERZE
====================================================
🎯 UNIVERZÁLNÍ WINDOWS EXE - VŠE V JEDNOM SOUBORU!

Podporované formáty:
• LABARA (CSV) - automatické mapování podle názvu souboru
• IMPA (Excel) - vyžaduje PO číslo
• DESCON (Excel) - automatické datum a zákazník

Výstup: CSV soubory do ~/Documents/Migrace_Vystupy/
Katalog: Vestavěný ALDE katalog (508 položek)

© 2025 - Migrace Objednávek - Final Standalone Solution
"""

import pandas as pd
import re
import os
import sys
import tkinter as tk
from tkinter import Toplevel, Entry, Label, Button, messagebox
from tkinter import filedialog
from datetime import datetime
import traceback

# ==============================================================================
# VESTAVĚNÝ ALDE KATALOG - KOMPLETNÍ VERZE (508 POLOŽEK) - SKUTEČNĚ KOMPLETNÍ!
# ==============================================================================

EMBEDDED_ALDE_CATALOG = [
    {"PART_NO": "ALDE006000009125000", "DESCRIPTION": "5083 litá 10,00"},
    {"PART_NO": "ALDE006000024891000", "DESCRIPTION": "5083 litá 100,00"},
    {"PART_NO": "ALDE006000024918000", "DESCRIPTION": "5083 litá 105,00"},
    {"PART_NO": "ALDE006000024916000", "DESCRIPTION": "5083 litá 110,00"},
    {"PART_NO": "ALDE006000024924000", "DESCRIPTION": "5083 litá 115,00"},
    {"PART_NO": "ALDE006000010138000", "DESCRIPTION": "5083 litá 12,00"},
    {"PART_NO": "ALDE006000024922000", "DESCRIPTION": "5083 litá 120,00"},
    {"PART_NO": "ALDE006000024915000", "DESCRIPTION": "5083 litá 125,00"},
    {"PART_NO": "ALDE006000024932000", "DESCRIPTION": "5083 litá 130,00"},
    {"PART_NO": "ALDE006000024897000", "DESCRIPTION": "5083 litá 135,00"},
    {"PART_NO": "ALDE006000024884000", "DESCRIPTION": "5083 litá 140,00"},
    {"PART_NO": "ALDE006000024913000", "DESCRIPTION": "5083 litá 145,00"},
    {"PART_NO": "ALDE006000013747000", "DESCRIPTION": "5083 litá 15,00"},
    {"PART_NO": "ALDE006000024923000", "DESCRIPTION": "5083 litá 150,00"},
    {"PART_NO": "ALDE006000024919000", "DESCRIPTION": "5083 litá 155,00"},
    {"PART_NO": "ALDE006000024898000", "DESCRIPTION": "5083 litá 16,00"},
    {"PART_NO": "ALDE006000024889000", "DESCRIPTION": "5083 litá 160,00"},
    {"PART_NO": "ALDE006000024899000", "DESCRIPTION": "5083 litá 165,00"},
    {"PART_NO": "ALDE006000024900000", "DESCRIPTION": "5083 litá 170,00"},
    {"PART_NO": "ALDE006000024887000", "DESCRIPTION": "5083 litá 180,00"},
    {"PART_NO": "ALDE006001042534000", "DESCRIPTION": "5083 litá 185,00"},
    {"PART_NO": "ALDE006000024890000", "DESCRIPTION": "5083 litá 190,00"},
    {"PART_NO": "ALDE006000017265000", "DESCRIPTION": "5083 litá 20,00"},
    {"PART_NO": "ALDE006000024911000", "DESCRIPTION": "5083 litá 200,00"},
    {"PART_NO": "ALDE006000024920000", "DESCRIPTION": "5083 litá 210,00"},
    {"PART_NO": "ALDE006000024931000", "DESCRIPTION": "5083 litá 220,00"},
    {"PART_NO": "ALDE006000024904000", "DESCRIPTION": "5083 litá 230,00"},
    {"PART_NO": "ALDE006000024930000", "DESCRIPTION": "5083 litá 240,00"},
    {"PART_NO": "ALDE006000018330000", "DESCRIPTION": "5083 litá 25,00"},
    {"PART_NO": "ALDE006000024896000", "DESCRIPTION": "5083 litá 250,00"},
    {"PART_NO": "ALDE006001000000000", "DESCRIPTION": "5083 litá 26,00"},
    {"PART_NO": "ALDE006000024895000", "DESCRIPTION": "5083 litá 260,00"},
    {"PART_NO": "ALDE006000024917000", "DESCRIPTION": "5083 litá 270,00"},
    {"PART_NO": "ALDE006001041156000", "DESCRIPTION": "5083 litá 275,00"},
    {"PART_NO": "ALDE006000024907000", "DESCRIPTION": "5083 litá 280,00"},
    {"PART_NO": "ALDE006001041157000", "DESCRIPTION": "5083 litá 285,00"},
    {"PART_NO": "ALDE006000024905000", "DESCRIPTION": "5083 litá 290,00"},
    {"PART_NO": "ALDE006000021723000", "DESCRIPTION": "5083 litá 30,00"},
    {"PART_NO": "ALDE006000024901000", "DESCRIPTION": "5083 litá 300,00"},
    {"PART_NO": "ALDE006001002327000", "DESCRIPTION": "5083 litá 320,00"},
    {"PART_NO": "ALDE006000021829000", "DESCRIPTION": "5083 litá 35,00"},
    {"PART_NO": "ALDE006000024007000", "DESCRIPTION": "5083 litá 40,00"},
    {"PART_NO": "ALDE006001041455000", "DESCRIPTION": "5083 litá 414,00"},
    {"PART_NO": "ALDE006000024912000", "DESCRIPTION": "5083 litá 45,00"},
    {"PART_NO": "ALDE006000024322000", "DESCRIPTION": "5083 litá 50,00"},
    {"PART_NO": "ALDE006000024341000", "DESCRIPTION": "5083 litá 55,00"},
    {"PART_NO": "ALDE006000024383000", "DESCRIPTION": "5083 litá 6,00"},
    {"PART_NO": "ALDE006000024909000", "DESCRIPTION": "5083 litá 60,00"},
    {"PART_NO": "ALDE006000024433000", "DESCRIPTION": "5083 litá 65,00"},
    {"PART_NO": "ALDE006000024888000", "DESCRIPTION": "5083 litá 70,00"},
    {"PART_NO": "ALDE006000024886000", "DESCRIPTION": "5083 litá 75,00"},
    {"PART_NO": "ALDE006000024885000", "DESCRIPTION": "5083 litá 80,00"},
    {"PART_NO": "ALDE006000024925000", "DESCRIPTION": "5083 litá 85,00"},
    {"PART_NO": "ALDE006000024508000", "DESCRIPTION": "5083 litá 90,00"},
    {"PART_NO": "ALDE006000024902000", "DESCRIPTION": "5083 litá 95,00"},
    {"PART_NO": "ALDE006010009125000", "DESCRIPTION": "5083 litá frézovaná 10,00"},
    {"PART_NO": "ALDE006010024914000", "DESCRIPTION": "5083 litá frézovaná 10,30"},
    {"PART_NO": "ALDE006010024891000", "DESCRIPTION": "5083 litá frézovaná 100,00"},
    {"PART_NO": "ALDE006010024929000", "DESCRIPTION": "5083 litá frézovaná 11,00"},
    {"PART_NO": "ALDE006010010138000", "DESCRIPTION": "5083 litá frézovaná 12,00"},
    {"PART_NO": "ALDE006010024926000", "DESCRIPTION": "5083 litá frézovaná 12,70"},
    {"PART_NO": "ALDE006011000001000", "DESCRIPTION": "5083 litá frézovaná 13,00"},
    {"PART_NO": "ALDE006011000002000", "DESCRIPTION": "5083 litá frézovaná 14,00"},
    {"PART_NO": "ALDE006010013747000", "DESCRIPTION": "5083 litá frézovaná 15,00"},
    {"PART_NO": "ALDE006010024898000", "DESCRIPTION": "5083 litá frézovaná 16,00"},
    {"PART_NO": "ALDE006010024933000", "DESCRIPTION": "5083 litá frézovaná 18,00"},
    {"PART_NO": "ALDE006010024921000", "DESCRIPTION": "5083 litá frézovaná 18,50"},
    {"PART_NO": "ALDE006010024883000", "DESCRIPTION": "5083 litá frézovaná 19,00"},
    {"PART_NO": "ALDE006011000507000", "DESCRIPTION": "5083 litá frézovaná 19,05"},
    {"PART_NO": "ALDE006010024910000", "DESCRIPTION": "5083 litá frézovaná 19,70"},
    {"PART_NO": "ALDE006010017265000", "DESCRIPTION": "5083 litá frézovaná 20,00"},
    {"PART_NO": "ALDE006010024903000", "DESCRIPTION": "5083 litá frézovaná 22,00"},
    {"PART_NO": "ALDE006010018330000", "DESCRIPTION": "5083 litá frézovaná 25,00"},
    {"PART_NO": "ALDE006010024934000", "DESCRIPTION": "5083 litá frézovaná 25,40"},
    {"PART_NO": "ALDE006010024882000", "DESCRIPTION": "5083 litá frézovaná 28,00"},
    {"PART_NO": "ALDE006010021723000", "DESCRIPTION": "5083 litá frézovaná 30,00"},
    {"PART_NO": "ALDE006010021829000", "DESCRIPTION": "5083 litá frézovaná 35,00"},
    {"PART_NO": "ALDE006010021905000", "DESCRIPTION": "5083 litá frézovaná 4,00"},
    {"PART_NO": "ALDE006010024007000", "DESCRIPTION": "5083 litá frézovaná 40,00"},
    {"PART_NO": "ALDE006010024892000", "DESCRIPTION": "5083 litá frézovaná 41,00"},
    {"PART_NO": "ALDE006010024912000", "DESCRIPTION": "5083 litá frézovaná 45,00"},
    {"PART_NO": "ALDE006010024057000", "DESCRIPTION": "5083 litá frézovaná 5,00"},
    {"PART_NO": "ALDE006010024322000", "DESCRIPTION": "5083 litá frézovaná 50,00"},
    {"PART_NO": "ALDE006010024341000", "DESCRIPTION": "5083 litá frézovaná 55,00"},
    {"PART_NO": "ALDE006010024383000", "DESCRIPTION": "5083 litá frézovaná 6,00"},
    {"PART_NO": "ALDE006010024909000", "DESCRIPTION": "5083 litá frézovaná 60,00"},
    {"PART_NO": "ALDE006010024433000", "DESCRIPTION": "5083 litá frézovaná 65,00"},
    {"PART_NO": "ALDE006010024888000", "DESCRIPTION": "5083 litá frézovaná 70,00"},
    {"PART_NO": "ALDE006010024468000", "DESCRIPTION": "5083 litá frézovaná 8,00"},
    {"PART_NO": "ALDE006010024885000", "DESCRIPTION": "5083 litá frézovaná 80,00"},
    {"PART_NO": "ALDE006020024933000", "DESCRIPTION": "5754 litá 18,00"},
    {"PART_NO": "ALDE006020018330000", "DESCRIPTION": "5754 litá 25,00"},
    {"PART_NO": "ALDE006020021723000", "DESCRIPTION": "5754 litá 30,00"},
    {"PART_NO": "ALDE006020024007000", "DESCRIPTION": "5754 litá 40,00"},
    {"PART_NO": "ALDE006020024912000", "DESCRIPTION": "5754 litá 45,00"},
    {"PART_NO": "ALDE006020024322000", "DESCRIPTION": "5754 litá 50,00"},
    {"PART_NO": "ALDE006020024909000", "DESCRIPTION": "5754 litá 60,00"},
    {"PART_NO": "ALDE006020024888000", "DESCRIPTION": "5754 litá 70,00"},
    {"PART_NO": "ALDE006020024885000", "DESCRIPTION": "5754 litá 80,00"},
    {"PART_NO": "ALDE006030009125000", "DESCRIPTION": "5754 litá frézovaná 10,00"},
    {"PART_NO": "ALDE006030010138000", "DESCRIPTION": "5754 litá frézovaná 12,00"},
    {"PART_NO": "ALDE006030013747000", "DESCRIPTION": "5754 litá frézovaná 15,00"},
    {"PART_NO": "ALDE006030017265000", "DESCRIPTION": "5754 litá frézovaná 20,00"},
    {"PART_NO": "ALDE006030018330000", "DESCRIPTION": "5754 litá frézovaná 25,00"},
    {"PART_NO": "ALDE006030021723000", "DESCRIPTION": "5754 litá frézovaná 30,00"},
    {"PART_NO": "ALDE006030021829000", "DESCRIPTION": "5754 litá frézovaná 35,00"},
    {"PART_NO": "ALDE006030024007000", "DESCRIPTION": "5754 litá frézovaná 40,00"},
    {"PART_NO": "ALDE006030024057000", "DESCRIPTION": "5754 litá frézovaná 5,00"},
    {"PART_NO": "ALDE006030024383000", "DESCRIPTION": "5754 litá frézovaná 6,00"},
    {"PART_NO": "ALDE006030024468000", "DESCRIPTION": "5754 litá frézovaná 8,00"},
    {"PART_NO": "ALDE007740024890000", "DESCRIPTION": "6061 litá 190,00"},
    {"PART_NO": "ALDE007741038988000", "DESCRIPTION": "6061 litá 195,00"},
    {"PART_NO": "ALDE006040024915000", "DESCRIPTION": "6061T651 125,00"},
    {"PART_NO": "ALDE006040024915007", "DESCRIPTION": "6061T651 125,00 AMS4027"},
    {"PART_NO": "ALDE007141000507007", "DESCRIPTION": "6061T651 válcovaná frézovaná 19,05 AMS4027"},
    {"PART_NO": "ALDE007141027722007", "DESCRIPTION": "6061T651 válcovaná frézovaná 22,23 AMS4027"},
    {"PART_NO": "ALDE007140024383007", "DESCRIPTION": "6061T651 válcovaná frézovaná 6,00 AMS4027"},
    {"PART_NO": "ALDE006050024898000", "DESCRIPTION": "7020 litá frézovaná 16,00"},
    {"PART_NO": "ALDE006050024468000", "DESCRIPTION": "7020 litá frézovaná 8,00"},
    {"PART_NO": "ALDE006060009125000", "DESCRIPTION": "7021 litá 10,00"},
    {"PART_NO": "ALDE006060024891000", "DESCRIPTION": "7021 litá 100,00"},
    {"PART_NO": "ALDE006060024889000", "DESCRIPTION": "7021 litá 160,00"},
    {"PART_NO": "ALDE006060017265000", "DESCRIPTION": "7021 litá 20,00"},
    {"PART_NO": "ALDE006060018330000", "DESCRIPTION": "7021 litá 25,00"},
    {"PART_NO": "ALDE006060021723000", "DESCRIPTION": "7021 litá 30,00"},
    {"PART_NO": "ALDE006060021829000", "DESCRIPTION": "7021 litá 35,00"},
    {"PART_NO": "ALDE006060024007000", "DESCRIPTION": "7021 litá 40,00"},
    {"PART_NO": "ALDE006060024912000", "DESCRIPTION": "7021 litá 45,00"},
    {"PART_NO": "ALDE006060024322000", "DESCRIPTION": "7021 litá 50,00"},
    {"PART_NO": "ALDE006060024341000", "DESCRIPTION": "7021 litá 55,00"},
    {"PART_NO": "ALDE006060024909000", "DESCRIPTION": "7021 litá 60,00"},
    {"PART_NO": "ALDE006060024888000", "DESCRIPTION": "7021 litá 70,00"},
    {"PART_NO": "ALDE006060024885000", "DESCRIPTION": "7021 litá 80,00"},
    {"PART_NO": "ALDE006060024508000", "DESCRIPTION": "7021 litá 90,00"},
    {"PART_NO": "ALDE006070009125000", "DESCRIPTION": "7021 litá frézovaná 10,00"},
    {"PART_NO": "ALDE006070010138000", "DESCRIPTION": "7021 litá frézovaná 12,00"},
    {"PART_NO": "ALDE006070013747000", "DESCRIPTION": "7021 litá frézovaná 15,00"},
    {"PART_NO": "ALDE006070024898000", "DESCRIPTION": "7021 litá frézovaná 16,00"},
    {"PART_NO": "ALDE006070017265000", "DESCRIPTION": "7021 litá frézovaná 20,00"},
    {"PART_NO": "ALDE006070018330000", "DESCRIPTION": "7021 litá frézovaná 25,00"},
    {"PART_NO": "ALDE006070021723000", "DESCRIPTION": "7021 litá frézovaná 30,00"},
    {"PART_NO": "ALDE006070021829000", "DESCRIPTION": "7021 litá frézovaná 35,00"},
    {"PART_NO": "ALDE006070024007000", "DESCRIPTION": "7021 litá frézovaná 40,00"},
    {"PART_NO": "ALDE006070024912000", "DESCRIPTION": "7021 litá frézovaná 45,00"},
    {"PART_NO": "ALDE006070024322000", "DESCRIPTION": "7021 litá frézovaná 50,00"},
    {"PART_NO": "ALDE006070024341000", "DESCRIPTION": "7021 litá frézovaná 55,00"},
    {"PART_NO": "ALDE006070024909000", "DESCRIPTION": "7021 litá frézovaná 60,00"},
    {"PART_NO": "ALDE006070024468000", "DESCRIPTION": "7021 litá frézovaná 8,00"},
    {"PART_NO": "ALDE002030009125000", "DESCRIPTION": "ENAW1050AH111 10,00"},
    {"PART_NO": "ALDE002030010138000", "DESCRIPTION": "ENAW1050AH111 12,00"},
    {"PART_NO": "ALDE002030013747000", "DESCRIPTION": "ENAW1050AH111 15,00"},
    {"PART_NO": "ALDE002030017265000", "DESCRIPTION": "ENAW1050AH111 20,00"},
    {"PART_NO": "ALDE002100009125000", "DESCRIPTION": "ENAW1050AH24 10,00"},
    {"PART_NO": "ALDE002100024468000", "DESCRIPTION": "ENAW1050AH24 8,00"},
    {"PART_NO": "ALDE002260009125000", "DESCRIPTION": "ENAW2017 válcovaná frézovaná 10,00"},
    {"PART_NO": "ALDE002260010138000", "DESCRIPTION": "ENAW2017 válcovaná frézovaná 12,00"},
    {"PART_NO": "ALDE002260013747000", "DESCRIPTION": "ENAW2017 válcovaná frézovaná 15,00"},
    {"PART_NO": "ALDE002260017265000", "DESCRIPTION": "ENAW2017 válcovaná frézovaná 20,00"},
    {"PART_NO": "ALDE002260018330000", "DESCRIPTION": "ENAW2017 válcovaná frézovaná 25,00"},
    {"PART_NO": "ALDE002260021723000", "DESCRIPTION": "ENAW2017 válcovaná frézovaná 30,00"},
    {"PART_NO": "ALDE002280024057000", "DESCRIPTION": "ENAW2017AT4 5,00"},
    {"PART_NO": "ALDE002290009125000", "DESCRIPTION": "ENAW2017AT451 10,00"},
    {"PART_NO": "ALDE002290024891000", "DESCRIPTION": "ENAW2017AT451 100,00"},
    {"PART_NO": "ALDE002290024916000", "DESCRIPTION": "ENAW2017AT451 110,00"},
    {"PART_NO": "ALDE002290010138000", "DESCRIPTION": "ENAW2017AT451 12,00"},
    {"PART_NO": "ALDE002290024922000", "DESCRIPTION": "ENAW2017AT451 120,00"},
    {"PART_NO": "ALDE002290024932000", "DESCRIPTION": "ENAW2017AT451 130,00"},
    {"PART_NO": "ALDE002290024884000", "DESCRIPTION": "ENAW2017AT451 140,00"},
    {"PART_NO": "ALDE002290013747000", "DESCRIPTION": "ENAW2017AT451 15,00"},
    {"PART_NO": "ALDE002290024923000", "DESCRIPTION": "ENAW2017AT451 150,00"},
    {"PART_NO": "ALDE002290024889000", "DESCRIPTION": "ENAW2017AT451 160,00"},
    {"PART_NO": "ALDE002290017265000", "DESCRIPTION": "ENAW2017AT451 20,00"},
    {"PART_NO": "ALDE002290018330000", "DESCRIPTION": "ENAW2017AT451 25,00"},
    {"PART_NO": "ALDE002290021723000", "DESCRIPTION": "ENAW2017AT451 30,00"},
    {"PART_NO": "ALDE002290021829000", "DESCRIPTION": "ENAW2017AT451 35,00"},
    {"PART_NO": "ALDE002290024007000", "DESCRIPTION": "ENAW2017AT451 40,00"},
    {"PART_NO": "ALDE002290024912000", "DESCRIPTION": "ENAW2017AT451 45,00"},
    {"PART_NO": "ALDE002290024057000", "DESCRIPTION": "ENAW2017AT451 5,00"},
    {"PART_NO": "ALDE002290024322000", "DESCRIPTION": "ENAW2017AT451 50,00"},
    {"PART_NO": "ALDE002290024341000", "DESCRIPTION": "ENAW2017AT451 55,00"},
    {"PART_NO": "ALDE002290024383000", "DESCRIPTION": "ENAW2017AT451 6,00"},
    {"PART_NO": "ALDE002290024909000", "DESCRIPTION": "ENAW2017AT451 60,00"},
    {"PART_NO": "ALDE002290024433000", "DESCRIPTION": "ENAW2017AT451 65,00"},
    {"PART_NO": "ALDE002290024888000", "DESCRIPTION": "ENAW2017AT451 70,00"},
    {"PART_NO": "ALDE002290024886000", "DESCRIPTION": "ENAW2017AT451 75,00"},
    {"PART_NO": "ALDE002290024468000", "DESCRIPTION": "ENAW2017AT451 8,00"},
    {"PART_NO": "ALDE002290024885000", "DESCRIPTION": "ENAW2017AT451 80,00"},
    {"PART_NO": "ALDE002290024508000", "DESCRIPTION": "ENAW2017AT451 90,00"},
    {"PART_NO": "ALDE002300024057000", "DESCRIPTION": "ENAW2024T3 5,00"},
    {"PART_NO": "ALDE002310010138000", "DESCRIPTION": "ENAW2024T351 12,00"},
    {"PART_NO": "ALDE002310013747000", "DESCRIPTION": "ENAW2024T351 15,00"},
    {"PART_NO": "ALDE002310024898000", "DESCRIPTION": "ENAW2024T351 16,00"},
    {"PART_NO": "ALDE002310017265000", "DESCRIPTION": "ENAW2024T351 20,00"},
    {"PART_NO": "ALDE002310018330000", "DESCRIPTION": "ENAW2024T351 25,00"},
    {"PART_NO": "ALDE002310024882000", "DESCRIPTION": "ENAW2024T351 28,00"},
    {"PART_NO": "ALDE002310021723000", "DESCRIPTION": "ENAW2024T351 30,00"},
    {"PART_NO": "ALDE002310024906000", "DESCRIPTION": "ENAW2024T351 32,00"},
    {"PART_NO": "ALDE002310024894000", "DESCRIPTION": "ENAW2024T351 36,00"},
    {"PART_NO": "ALDE002310024007000", "DESCRIPTION": "ENAW2024T351 40,00"},
    {"PART_NO": "ALDE002310024912000", "DESCRIPTION": "ENAW2024T351 45,00"},
    {"PART_NO": "ALDE002310024322000", "DESCRIPTION": "ENAW2024T351 50,00"},
    {"PART_NO": "ALDE002311021163000", "DESCRIPTION": "ENAW2024T351 50,80"},
    {"PART_NO": "ALDE002310024341000", "DESCRIPTION": "ENAW2024T351 55,00"},
    {"PART_NO": "ALDE002310024383000", "DESCRIPTION": "ENAW2024T351 6,00"},
    {"PART_NO": "ALDE002311000003000", "DESCRIPTION": "ENAW2024T351 63,00"},
    {"PART_NO": "ALDE002310024433000", "DESCRIPTION": "ENAW2024T351 65,00"},
    {"PART_NO": "ALDE002310024935000", "DESCRIPTION": "ENAW2024T351 7,00"},
    {"PART_NO": "ALDE002310024888000", "DESCRIPTION": "ENAW2024T351 70,00"},
    {"PART_NO": "ALDE002310024885000", "DESCRIPTION": "ENAW2024T351 80,00"},
    {"PART_NO": "ALDE002310024508000", "DESCRIPTION": "ENAW2024T351 90,00"},
    {"PART_NO": "ALDE002350024906000", "DESCRIPTION": "ENAW2618AT851 32,00"},
    {"PART_NO": "ALDE002780009125000", "DESCRIPTION": "ENAW5083H111 10,00"},
    {"PART_NO": "ALDE002780009125617", "DESCRIPTION": "ENAW5083H111 10,00 NS"},
    {"PART_NO": "ALDE002780024891000", "DESCRIPTION": "ENAW5083H111 100,00"},
    {"PART_NO": "ALDE002780010138000", "DESCRIPTION": "ENAW5083H111 12,00"},
    {"PART_NO": "ALDE002780010138617", "DESCRIPTION": "ENAW5083H111 12,00 NS"},
    {"PART_NO": "ALDE002780024922000", "DESCRIPTION": "ENAW5083H111 120,00"},
    {"PART_NO": "ALDE002780024932000", "DESCRIPTION": "ENAW5083H111 130,00"},
    {"PART_NO": "ALDE002780024884000", "DESCRIPTION": "ENAW5083H111 140,00"},
    {"PART_NO": "ALDE002780013747000", "DESCRIPTION": "ENAW5083H111 15,00"},
    {"PART_NO": "ALDE002780024923000", "DESCRIPTION": "ENAW5083H111 150,00"},
    {"PART_NO": "ALDE002780024898000", "DESCRIPTION": "ENAW5083H111 16,00"},
    {"PART_NO": "ALDE002780017265000", "DESCRIPTION": "ENAW5083H111 20,00"},
    {"PART_NO": "ALDE002780018330000", "DESCRIPTION": "ENAW5083H111 25,00"},
    {"PART_NO": "ALDE002780021723000", "DESCRIPTION": "ENAW5083H111 30,00"},
    {"PART_NO": "ALDE002780021829000", "DESCRIPTION": "ENAW5083H111 35,00"},
    {"PART_NO": "ALDE002780021905000", "DESCRIPTION": "ENAW5083H111 4,00"},
    {"PART_NO": "ALDE002780024007000", "DESCRIPTION": "ENAW5083H111 40,00"},
    {"PART_NO": "ALDE002780024912000", "DESCRIPTION": "ENAW5083H111 45,00"},
    {"PART_NO": "ALDE002780024057000", "DESCRIPTION": "ENAW5083H111 5,00"},
    {"PART_NO": "ALDE002780024322000", "DESCRIPTION": "ENAW5083H111 50,00"},
    {"PART_NO": "ALDE002780024341000", "DESCRIPTION": "ENAW5083H111 55,00"},
    {"PART_NO": "ALDE002780024383000", "DESCRIPTION": "ENAW5083H111 6,00"},
    {"PART_NO": "ALDE002780024909000", "DESCRIPTION": "ENAW5083H111 60,00"},
    {"PART_NO": "ALDE002780024433000", "DESCRIPTION": "ENAW5083H111 65,00"},
    {"PART_NO": "ALDE002780024888000", "DESCRIPTION": "ENAW5083H111 70,00"},
    {"PART_NO": "ALDE002780024886000", "DESCRIPTION": "ENAW5083H111 75,00"},
    {"PART_NO": "ALDE002780024468000", "DESCRIPTION": "ENAW5083H111 8,00"},
    {"PART_NO": "ALDE002780024468617", "DESCRIPTION": "ENAW5083H111 8,00 NS"},
    {"PART_NO": "ALDE002780024885000", "DESCRIPTION": "ENAW5083H111 80,00"},
    {"PART_NO": "ALDE002780024925000", "DESCRIPTION": "ENAW5083H111 85,00"},
    {"PART_NO": "ALDE002780024508000", "DESCRIPTION": "ENAW5083H111 90,00"},
    {"PART_NO": "ALDE002780024902000", "DESCRIPTION": "ENAW5083H111 95,00"},
    {"PART_NO": "ALDE002860009125000", "DESCRIPTION": "ENAW5754H111 10,00"},
    {"PART_NO": "ALDE002860009125617", "DESCRIPTION": "ENAW5754H111 10,00 NS"},
    {"PART_NO": "ALDE002860024891000", "DESCRIPTION": "ENAW5754H111 100,00"},
    {"PART_NO": "ALDE002860024916000", "DESCRIPTION": "ENAW5754H111 110,00"},
    {"PART_NO": "ALDE002860010138000", "DESCRIPTION": "ENAW5754H111 12,00"},
    {"PART_NO": "ALDE002860010138489", "DESCRIPTION": "ENAW5754H111 12,00 Mn"},
    {"PART_NO": "ALDE002860010138617", "DESCRIPTION": "ENAW5754H111 12,00 NS"},
    {"PART_NO": "ALDE002860024922000", "DESCRIPTION": "ENAW5754H111 120,00"},
    {"PART_NO": "ALDE002860013747000", "DESCRIPTION": "ENAW5754H111 15,00"},
    {"PART_NO": "ALDE002860013747489", "DESCRIPTION": "ENAW5754H111 15,00 Mn"},
    {"PART_NO": "ALDE002860024898000", "DESCRIPTION": "ENAW5754H111 16,00"},
    {"PART_NO": "ALDE002860024898489", "DESCRIPTION": "ENAW5754H111 16,00 Mn"},
    {"PART_NO": "ALDE002860014125000", "DESCRIPTION": "ENAW5754H111 2,00"},
    {"PART_NO": "ALDE002860017265000", "DESCRIPTION": "ENAW5754H111 20,00"},
    {"PART_NO": "ALDE002860017265489", "DESCRIPTION": "ENAW5754H111 20,00 Mn"},
    {"PART_NO": "ALDE002860018330000", "DESCRIPTION": "ENAW5754H111 25,00"},
    {"PART_NO": "ALDE002860018330489", "DESCRIPTION": "ENAW5754H111 25,00 Mn"},
    {"PART_NO": "ALDE002860018576000", "DESCRIPTION": "ENAW5754H111 3,00"},
    {"PART_NO": "ALDE002860021771000", "DESCRIPTION": "ENAW5754H111 3,50"},
    {"PART_NO": "ALDE002860021723000", "DESCRIPTION": "ENAW5754H111 30,00"},
    {"PART_NO": "ALDE002860021723489", "DESCRIPTION": "ENAW5754H111 30,00 Mn"},
    {"PART_NO": "ALDE002860021829000", "DESCRIPTION": "ENAW5754H111 35,00"},
    {"PART_NO": "ALDE002860021905000", "DESCRIPTION": "ENAW5754H111 4,00"},
    {"PART_NO": "ALDE002860024007000", "DESCRIPTION": "ENAW5754H111 40,00"},
    {"PART_NO": "ALDE002860024007489", "DESCRIPTION": "ENAW5754H111 40,00 Mn"},
    {"PART_NO": "ALDE002860024912000", "DESCRIPTION": "ENAW5754H111 45,00"},
    {"PART_NO": "ALDE002860024057000", "DESCRIPTION": "ENAW5754H111 5,00"},
    {"PART_NO": "ALDE002860024322000", "DESCRIPTION": "ENAW5754H111 50,00"},
    {"PART_NO": "ALDE002860024341000", "DESCRIPTION": "ENAW5754H111 55,00"},
    {"PART_NO": "ALDE002860024383000", "DESCRIPTION": "ENAW5754H111 6,00"},
    {"PART_NO": "ALDE002860024909000", "DESCRIPTION": "ENAW5754H111 60,00"},
    {"PART_NO": "ALDE002860024888000", "DESCRIPTION": "ENAW5754H111 70,00"},
    {"PART_NO": "ALDE002860024468000", "DESCRIPTION": "ENAW5754H111 8,00"},
    {"PART_NO": "ALDE002860024468617", "DESCRIPTION": "ENAW5754H111 8,00 NS"},
    {"PART_NO": "ALDE002860024885000", "DESCRIPTION": "ENAW5754H111 80,00"},
    {"PART_NO": "ALDE002860024508000", "DESCRIPTION": "ENAW5754H111 90,00"},
    {"PART_NO": "ALDE002870024907000", "DESCRIPTION": "ENAW5754H112 280,00"},
    {"PART_NO": "ALDE002870024902000", "DESCRIPTION": "ENAW5754H112 95,00"},
    {"PART_NO": "ALDE002910021905000", "DESCRIPTION": "ENAW5754H22 4,00"},
    {"PART_NO": "ALDE002910024057000", "DESCRIPTION": "ENAW5754H22 5,00"},
    {"PART_NO": "ALDE002910024383000", "DESCRIPTION": "ENAW5754H22 6,00"},
    {"PART_NO": "ALDE002910024468000", "DESCRIPTION": "ENAW5754H22 8,00"},
    {"PART_NO": "ALDE003080009125000", "DESCRIPTION": "ENAW6061T6 10,00"},
    {"PART_NO": "ALDE003081014870000", "DESCRIPTION": "ENAW6061T6 118,00"},
    {"PART_NO": "ALDE003081014871000", "DESCRIPTION": "ENAW6061T6 126,00"},
    {"PART_NO": "ALDE003080024932000", "DESCRIPTION": "ENAW6061T6 130,00"},
    {"PART_NO": "ALDE003080024884000", "DESCRIPTION": "ENAW6061T6 140,00"},
    {"PART_NO": "ALDE003080024913000", "DESCRIPTION": "ENAW6061T6 145,00"},
    {"PART_NO": "ALDE003080024923000", "DESCRIPTION": "ENAW6061T6 150,00"},
    {"PART_NO": "ALDE003080024900000", "DESCRIPTION": "ENAW6061T6 170,00"},
    {"PART_NO": "ALDE003080024890000", "DESCRIPTION": "ENAW6061T6 190,00"},
    {"PART_NO": "ALDE003080024930000", "DESCRIPTION": "ENAW6061T6 240,00"},
    {"PART_NO": "ALDE003081014869000", "DESCRIPTION": "ENAW6061T6 54,00"},
    {"PART_NO": "ALDE003080024341000", "DESCRIPTION": "ENAW6061T6 55,00"},
    {"PART_NO": "ALDE003080024433000", "DESCRIPTION": "ENAW6061T6 65,00"},
    {"PART_NO": "ALDE003081014909000", "DESCRIPTION": "ENAW6061T6 93,00"},
    {"PART_NO": "ALDE003100009125000", "DESCRIPTION": "ENAW6061T651 10,00"},
    {"PART_NO": "ALDE003100024891000", "DESCRIPTION": "ENAW6061T651 100,00"},
    {"PART_NO": "ALDE003100024916000", "DESCRIPTION": "ENAW6061T651 110,00"},
    {"PART_NO": "ALDE003100010138000", "DESCRIPTION": "ENAW6061T651 12,00"},
    {"PART_NO": "ALDE003100024922000", "DESCRIPTION": "ENAW6061T651 120,00"},
    {"PART_NO": "ALDE003100024932000", "DESCRIPTION": "ENAW6061T651 130,00"},
    {"PART_NO": "ALDE003100024932007", "DESCRIPTION": "ENAW6061T651 130,00 AMS4027"},
    {"PART_NO": "ALDE003100024884000", "DESCRIPTION": "ENAW6061T651 140,00"},
    {"PART_NO": "ALDE003100013747000", "DESCRIPTION": "ENAW6061T651 15,00"},
    {"PART_NO": "ALDE003100024923000", "DESCRIPTION": "ENAW6061T651 150,00"},
    {"PART_NO": "ALDE003100024898000", "DESCRIPTION": "ENAW6061T651 16,00"},
    {"PART_NO": "ALDE003100024900000", "DESCRIPTION": "ENAW6061T651 170,00"},
    {"PART_NO": "ALDE003100024887000", "DESCRIPTION": "ENAW6061T651 180,00"},
    {"PART_NO": "ALDE003100017265000", "DESCRIPTION": "ENAW6061T651 20,00"},
    {"PART_NO": "ALDE003100017265007", "DESCRIPTION": "ENAW6061T651 20,00 AMS4027"},
    {"PART_NO": "ALDE003100018330000", "DESCRIPTION": "ENAW6061T651 25,00"},
    {"PART_NO": "ALDE003100018330007", "DESCRIPTION": "ENAW6061T651 25,00 AMS4027"},
    {"PART_NO": "ALDE003100024934000", "DESCRIPTION": "ENAW6061T651 25,40"},
    {"PART_NO": "ALDE003100021723000", "DESCRIPTION": "ENAW6061T651 30,00"},
    {"PART_NO": "ALDE003100021829000", "DESCRIPTION": "ENAW6061T651 35,00"},
    {"PART_NO": "ALDE003100024007000", "DESCRIPTION": "ENAW6061T651 40,00"},
    {"PART_NO": "ALDE003100024912000", "DESCRIPTION": "ENAW6061T651 45,00"},
    {"PART_NO": "ALDE003100024322000", "DESCRIPTION": "ENAW6061T651 50,00"},
    {"PART_NO": "ALDE003100024341000", "DESCRIPTION": "ENAW6061T651 55,00"},
    {"PART_NO": "ALDE003100024341007", "DESCRIPTION": "ENAW6061T651 55,00 AMS4027"},
    {"PART_NO": "ALDE003100024383000", "DESCRIPTION": "ENAW6061T651 6,00"},
    {"PART_NO": "ALDE003100024909000", "DESCRIPTION": "ENAW6061T651 60,00"},
    {"PART_NO": "ALDE003100024888000", "DESCRIPTION": "ENAW6061T651 70,00"},
    {"PART_NO": "ALDE003100024468000", "DESCRIPTION": "ENAW6061T651 8,00"},
    {"PART_NO": "ALDE003100024885000", "DESCRIPTION": "ENAW6061T651 80,00"},
    {"PART_NO": "ALDE003100024508000", "DESCRIPTION": "ENAW6061T651 90,00"},
    {"PART_NO": "ALDE003150009125000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 10,00"},
    {"PART_NO": "ALDE003150010138000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 12,00"},
    {"PART_NO": "ALDE003150013747000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 15,00"},
    {"PART_NO": "ALDE003150017265000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 20,00"},
    {"PART_NO": "ALDE003150018330000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 25,00"},
    {"PART_NO": "ALDE003150021723000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 30,00"},
    {"PART_NO": "ALDE003150024007000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 40,00"},
    {"PART_NO": "ALDE003150024322000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 50,00"},
    {"PART_NO": "ALDE003150024383000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 6,00"},
    {"PART_NO": "ALDE003150024468000", "DESCRIPTION": "ENAW6082 válcovaná frézovaná 8,00"},
    {"PART_NO": "ALDE003170024889000", "DESCRIPTION": "ENAW6082T6 160,00"},
    {"PART_NO": "ALDE003170024907000", "DESCRIPTION": "ENAW6082T6 280,00"},
    {"PART_NO": "ALDE003170024057000", "DESCRIPTION": "ENAW6082T6 5,00"},
    {"PART_NO": "ALDE003170024383000", "DESCRIPTION": "ENAW6082T6 6,00"},
    {"PART_NO": "ALDE003180009125000", "DESCRIPTION": "ENAW6082T651 10,00"},
    {"PART_NO": "ALDE003180024891000", "DESCRIPTION": "ENAW6082T651 100,00"},
    {"PART_NO": "ALDE003180024916000", "DESCRIPTION": "ENAW6082T651 110,00"},
    {"PART_NO": "ALDE003180010138000", "DESCRIPTION": "ENAW6082T651 12,00"},
    {"PART_NO": "ALDE003180024926000", "DESCRIPTION": "ENAW6082T651 12,70"},
    {"PART_NO": "ALDE003180024922000", "DESCRIPTION": "ENAW6082T651 120,00"},
    {"PART_NO": "ALDE003180024932000", "DESCRIPTION": "ENAW6082T651 130,00"},
    {"PART_NO": "ALDE003180024884000", "DESCRIPTION": "ENAW6082T651 140,00"},
    {"PART_NO": "ALDE003180013747000", "DESCRIPTION": "ENAW6082T651 15,00"},
    {"PART_NO": "ALDE003180024923000", "DESCRIPTION": "ENAW6082T651 150,00"},
    {"PART_NO": "ALDE003180024898000", "DESCRIPTION": "ENAW6082T651 16,00"},
    {"PART_NO": "ALDE003180024889000", "DESCRIPTION": "ENAW6082T651 160,00"},
    {"PART_NO": "ALDE003180024887000", "DESCRIPTION": "ENAW6082T651 180,00"},
    {"PART_NO": "ALDE003180017265000", "DESCRIPTION": "ENAW6082T651 20,00"},
    {"PART_NO": "ALDE003180024893000", "DESCRIPTION": "ENAW6082T651 20,10"},
    {"PART_NO": "ALDE003180024911000", "DESCRIPTION": "ENAW6082T651 200,00"},
    {"PART_NO": "ALDE003180024928000", "DESCRIPTION": "ENAW6082T651 21,00"},
    {"PART_NO": "ALDE003180024931000", "DESCRIPTION": "ENAW6082T651 220,00"},
    {"PART_NO": "ALDE003180018330000", "DESCRIPTION": "ENAW6082T651 25,00"},
    {"PART_NO": "ALDE003180021723000", "DESCRIPTION": "ENAW6082T651 30,00"},
    {"PART_NO": "ALDE003180021829000", "DESCRIPTION": "ENAW6082T651 35,00"},
    {"PART_NO": "ALDE003180024007000", "DESCRIPTION": "ENAW6082T651 40,00"},
    {"PART_NO": "ALDE003181038355000", "DESCRIPTION": "ENAW6082T651 43,50"},
    {"PART_NO": "ALDE003180024912000", "DESCRIPTION": "ENAW6082T651 45,00"},
    {"PART_NO": "ALDE003180024057000", "DESCRIPTION": "ENAW6082T651 5,00"},
    {"PART_NO": "ALDE003180024322000", "DESCRIPTION": "ENAW6082T651 50,00"},
    {"PART_NO": "ALDE003180024341000", "DESCRIPTION": "ENAW6082T651 55,00"},
    {"PART_NO": "ALDE003180024927000", "DESCRIPTION": "ENAW6082T651 56,00"},
    {"PART_NO": "ALDE003180024383000", "DESCRIPTION": "ENAW6082T651 6,00"},
    {"PART_NO": "ALDE003180024909000", "DESCRIPTION": "ENAW6082T651 60,00"},
    {"PART_NO": "ALDE003180024433000", "DESCRIPTION": "ENAW6082T651 65,00"},
    {"PART_NO": "ALDE003180024888000", "DESCRIPTION": "ENAW6082T651 70,00"},
    {"PART_NO": "ALDE003180024886000", "DESCRIPTION": "ENAW6082T651 75,00"},
    {"PART_NO": "ALDE003180024468000", "DESCRIPTION": "ENAW6082T651 8,00"},
    {"PART_NO": "ALDE003180024885000", "DESCRIPTION": "ENAW6082T651 80,00"},
    {"PART_NO": "ALDE003180024925000", "DESCRIPTION": "ENAW6082T651 85,00"},
    {"PART_NO": "ALDE003180024508000", "DESCRIPTION": "ENAW6082T651 90,00"},
    {"PART_NO": "ALDE003180024902000", "DESCRIPTION": "ENAW6082T651 95,00"},
    {"PART_NO": "ALDE008051002327000", "DESCRIPTION": "ENAW6082T652 320,00"},
    {"PART_NO": "ALDE003220009125000", "DESCRIPTION": "ENAW7020T6 10,00"},
    {"PART_NO": "ALDE003220013747000", "DESCRIPTION": "ENAW7020T6 15,00"},
    {"PART_NO": "ALDE003220018330000", "DESCRIPTION": "ENAW7020T6 25,00"},
    {"PART_NO": "ALDE003220024057000", "DESCRIPTION": "ENAW7020T6 5,00"},
    {"PART_NO": "ALDE003220024383000", "DESCRIPTION": "ENAW7020T6 6,00"},
    {"PART_NO": "ALDE003230009125000", "DESCRIPTION": "ENAW7020T651 10,00"},
    {"PART_NO": "ALDE003230013747000", "DESCRIPTION": "ENAW7020T651 15,00"},
    {"PART_NO": "ALDE003230017265000", "DESCRIPTION": "ENAW7020T651 20,00"},
    {"PART_NO": "ALDE003230018330000", "DESCRIPTION": "ENAW7020T651 25,00"},
    {"PART_NO": "ALDE003230024057000", "DESCRIPTION": "ENAW7020T651 5,00"},
    {"PART_NO": "ALDE003230024383000", "DESCRIPTION": "ENAW7020T651 6,00"},
    {"PART_NO": "ALDE003250009125000", "DESCRIPTION": "ENAW7022T651 10,00"},
    {"PART_NO": "ALDE003250024891000", "DESCRIPTION": "ENAW7022T651 100,00"},
    {"PART_NO": "ALDE003250024916000", "DESCRIPTION": "ENAW7022T651 110,00"},
    {"PART_NO": "ALDE003250010138000", "DESCRIPTION": "ENAW7022T651 12,00"},
    {"PART_NO": "ALDE003250024922000", "DESCRIPTION": "ENAW7022T651 120,00"},
    {"PART_NO": "ALDE003250024932000", "DESCRIPTION": "ENAW7022T651 130,00"},
    {"PART_NO": "ALDE003250024884000", "DESCRIPTION": "ENAW7022T651 140,00"},
    {"PART_NO": "ALDE003250013747000", "DESCRIPTION": "ENAW7022T651 15,00"},
    {"PART_NO": "ALDE003250024923000", "DESCRIPTION": "ENAW7022T651 150,00"},
    {"PART_NO": "ALDE003250024887000", "DESCRIPTION": "ENAW7022T651 180,00"},
    {"PART_NO": "ALDE003250017265000", "DESCRIPTION": "ENAW7022T651 20,00"},
    {"PART_NO": "ALDE003250018330000", "DESCRIPTION": "ENAW7022T651 25,00"},
    {"PART_NO": "ALDE003250024896000", "DESCRIPTION": "ENAW7022T651 250,00"},
    {"PART_NO": "ALDE003250021723000", "DESCRIPTION": "ENAW7022T651 30,00"},
    {"PART_NO": "ALDE003250021829000", "DESCRIPTION": "ENAW7022T651 35,00"},
    {"PART_NO": "ALDE003250024007000", "DESCRIPTION": "ENAW7022T651 40,00"},
    {"PART_NO": "ALDE003250024912000", "DESCRIPTION": "ENAW7022T651 45,00"},
    {"PART_NO": "ALDE003250024322000", "DESCRIPTION": "ENAW7022T651 50,00"},
    {"PART_NO": "ALDE003250024341000", "DESCRIPTION": "ENAW7022T651 55,00"},
    {"PART_NO": "ALDE003250024383000", "DESCRIPTION": "ENAW7022T651 6,00"},
    {"PART_NO": "ALDE003250024909000", "DESCRIPTION": "ENAW7022T651 60,00"},
    {"PART_NO": "ALDE003250024433000", "DESCRIPTION": "ENAW7022T651 65,00"},
    {"PART_NO": "ALDE003250024888000", "DESCRIPTION": "ENAW7022T651 70,00"},
    {"PART_NO": "ALDE003250024886000", "DESCRIPTION": "ENAW7022T651 75,00"},
    {"PART_NO": "ALDE003250024468000", "DESCRIPTION": "ENAW7022T651 8,00"},
    {"PART_NO": "ALDE003250024885000", "DESCRIPTION": "ENAW7022T651 80,00"},
    {"PART_NO": "ALDE003250024508000", "DESCRIPTION": "ENAW7022T651 90,00"},
    {"PART_NO": "ALDE003260009125000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 10,00"},
    {"PART_NO": "ALDE003260010138000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 12,00"},
    {"PART_NO": "ALDE003260013747000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 15,00"},
    {"PART_NO": "ALDE003260024898000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 16,00"},
    {"PART_NO": "ALDE003260017265000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 20,00"},
    {"PART_NO": "ALDE003260018330000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 25,00"},
    {"PART_NO": "ALDE003260021723000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 30,00"},
    {"PART_NO": "ALDE003260021829000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 35,00"},
    {"PART_NO": "ALDE003260024007000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 40,00"},
    {"PART_NO": "ALDE003260024912000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 45,00"},
    {"PART_NO": "ALDE003260024322000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 50,00"},
    {"PART_NO": "ALDE003260024383000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 6,00"},
    {"PART_NO": "ALDE003260024909000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 60,00"},
    {"PART_NO": "ALDE003260024468000", "DESCRIPTION": "ENAW7075 válcovaná frézovaná 8,00"},
    {"PART_NO": "ALDE003270024889000", "DESCRIPTION": "ENAW7075T6 160,00"},
    {"PART_NO": "ALDE003270024900000", "DESCRIPTION": "ENAW7075T6 170,00"},
    {"PART_NO": "ALDE003270024887000", "DESCRIPTION": "ENAW7075T6 180,00"},
    {"PART_NO": "ALDE003270024890000", "DESCRIPTION": "ENAW7075T6 190,00"},
    {"PART_NO": "ALDE003270024911000", "DESCRIPTION": "ENAW7075T6 200,00"},
    {"PART_NO": "ALDE003270024057000", "DESCRIPTION": "ENAW7075T6 5,00"},
    {"PART_NO": "ALDE003280009125000", "DESCRIPTION": "ENAW7075T651 10,00"},
    {"PART_NO": "ALDE003280024891000", "DESCRIPTION": "ENAW7075T651 100,00"},
    {"PART_NO": "ALDE003280024916000", "DESCRIPTION": "ENAW7075T651 110,00"},
    {"PART_NO": "ALDE003280010138000", "DESCRIPTION": "ENAW7075T651 12,00"},
    {"PART_NO": "ALDE003280024922000", "DESCRIPTION": "ENAW7075T651 120,00"},
    {"PART_NO": "ALDE003280024932000", "DESCRIPTION": "ENAW7075T651 130,00"},
    {"PART_NO": "ALDE003280024884000", "DESCRIPTION": "ENAW7075T651 140,00"},
    {"PART_NO": "ALDE003280013747000", "DESCRIPTION": "ENAW7075T651 15,00"},
    {"PART_NO": "ALDE003280024923000", "DESCRIPTION": "ENAW7075T651 150,00"},
    {"PART_NO": "ALDE003280024898000", "DESCRIPTION": "ENAW7075T651 16,00"},
    {"PART_NO": "ALDE003280024889000", "DESCRIPTION": "ENAW7075T651 160,00"},
    {"PART_NO": "ALDE003280024900000", "DESCRIPTION": "ENAW7075T651 170,00"},
    {"PART_NO": "ALDE003280024887000", "DESCRIPTION": "ENAW7075T651 180,00"},
    {"PART_NO": "ALDE003280017265000", "DESCRIPTION": "ENAW7075T651 20,00"},
    {"PART_NO": "ALDE003280024908000", "DESCRIPTION": "ENAW7075T651 20,50"},
    {"PART_NO": "ALDE003280024911000", "DESCRIPTION": "ENAW7075T651 200,00"},
    {"PART_NO": "ALDE003280018330000", "DESCRIPTION": "ENAW7075T651 25,00"},
    {"PART_NO": "ALDE003280021723000", "DESCRIPTION": "ENAW7075T651 30,00"},
    {"PART_NO": "ALDE003280024906000", "DESCRIPTION": "ENAW7075T651 32,00"},
    {"PART_NO": "ALDE003280021829000", "DESCRIPTION": "ENAW7075T651 35,00"},
    {"PART_NO": "ALDE003281030837000", "DESCRIPTION": "ENAW7075T651 38,00"},
    {"PART_NO": "ALDE003280024007000", "DESCRIPTION": "ENAW7075T651 40,00"},
    {"PART_NO": "ALDE003280024912000", "DESCRIPTION": "ENAW7075T651 45,00"},
    {"PART_NO": "ALDE003280024057000", "DESCRIPTION": "ENAW7075T651 5,00"},
    {"PART_NO": "ALDE003280024322000", "DESCRIPTION": "ENAW7075T651 50,00"},
    {"PART_NO": "ALDE003280024341000", "DESCRIPTION": "ENAW7075T651 55,00"},
    {"PART_NO": "ALDE003280024383000", "DESCRIPTION": "ENAW7075T651 6,00"},
    {"PART_NO": "ALDE003280024909000", "DESCRIPTION": "ENAW7075T651 60,00"},
    {"PART_NO": "ALDE003280024433000", "DESCRIPTION": "ENAW7075T651 65,00"},
    {"PART_NO": "ALDE003280024935000", "DESCRIPTION": "ENAW7075T651 7,00"},
    {"PART_NO": "ALDE003280024888000", "DESCRIPTION": "ENAW7075T651 70,00"},
    {"PART_NO": "ALDE003280024886000", "DESCRIPTION": "ENAW7075T651 75,00"},
    {"PART_NO": "ALDE003280024468000", "DESCRIPTION": "ENAW7075T651 8,00"},
    {"PART_NO": "ALDE003280024885000", "DESCRIPTION": "ENAW7075T651 80,00"},
    {"PART_NO": "ALDE003280024925000", "DESCRIPTION": "ENAW7075T651 85,00"},
    {"PART_NO": "ALDE003280024508000", "DESCRIPTION": "ENAW7075T651 90,00"},
    {"PART_NO": "ALDE003290024887145", "DESCRIPTION": "ENAW7075T652 180,00 kovaná"},
    {"PART_NO": "ALDE003290024911145", "DESCRIPTION": "ENAW7075T652 200,00 kovaná"},
    {"PART_NO": "ALDE003290024931145", "DESCRIPTION": "ENAW7075T652 220,00 kovaná"},
    {"PART_NO": "ALDE003310010138000", "DESCRIPTION": "ENAW7075T7351 12,00"},
    {"PART_NO": "ALDE003310024909000", "DESCRIPTION": "ENAW7075T7351 60,00"},
    {"PART_NO": "ALDE003330024923000", "DESCRIPTION": "ENAW7122T651 150,00"},
    {"PART_NO": "ALDE003330024889000", "DESCRIPTION": "ENAW7122T651 160,00"},
    {"PART_NO": "ALDE003330024887000", "DESCRIPTION": "ENAW7122T651 180,00"},
    {"PART_NO": "ALDE003330024911000", "DESCRIPTION": "ENAW7122T651 200,00"},
    {"PART_NO": "ALDE003330024931000", "DESCRIPTION": "ENAW7122T651 220,00"},
    {"PART_NO": "ALDE003330024896000", "DESCRIPTION": "ENAW7122T651 250,00"},
    {"PART_NO": "ALDE003340024884000", "DESCRIPTION": "ENAW7122T652 140,00"},
    {"PART_NO": "ALDE003340024907000", "DESCRIPTION": "ENAW7122T652 280,00"},
    {"PART_NO": "ALDE006681003209000", "DESCRIPTION": "Materiál universální hliník"},
    {"PART_NO": "ALDE004820009125000", "DESCRIPTION": "UNIDAL 10,00"},
    {"PART_NO": "ALDE004820010138000", "DESCRIPTION": "UNIDAL 12,00"},
    {"PART_NO": "ALDE004820013747000", "DESCRIPTION": "UNIDAL 15,00"},
    {"PART_NO": "ALDE004820017265000", "DESCRIPTION": "UNIDAL 20,00"},
    {"PART_NO": "ALDE004820018330000", "DESCRIPTION": "UNIDAL 25,00"},
    {"PART_NO": "ALDE004820021723000", "DESCRIPTION": "UNIDAL 30,00"},
    {"PART_NO": "ALDE004820021829000", "DESCRIPTION": "UNIDAL 35,00"},
    {"PART_NO": "ALDE004820024007000", "DESCRIPTION": "UNIDAL 40,00"},
    {"PART_NO": "ALDE004820024322000", "DESCRIPTION": "UNIDAL 50,00"},
    {"PART_NO": "ALDE004820024909000", "DESCRIPTION": "UNIDAL 60,00"},
    {"PART_NO": "ALDE004820024468000", "DESCRIPTION": "UNIDAL 8,00"},
    {"PART_NO": "ALDE004830024916000", "DESCRIPTION": "WELDURAL 110,00"},
    {"PART_NO": "ALDE004830024924000", "DESCRIPTION": "WELDURAL 115,00"},
    {"PART_NO": "ALDE004830024922000", "DESCRIPTION": "WELDURAL 120,00"},
    {"PART_NO": "ALDE004830024932000", "DESCRIPTION": "WELDURAL 130,00"},
    {"PART_NO": "ALDE004830024884000", "DESCRIPTION": "WELDURAL 140,00"},
    {"PART_NO": "ALDE004830024923000", "DESCRIPTION": "WELDURAL 150,00"},
]

def get_embedded_catalog():
    """Vrátí vestavěný katalog jako pandas DataFrame"""
    return pd.DataFrame(EMBEDDED_ALDE_CATALOG)

# ==============================================================================
# ČÁST 0: Dialogové okno pro zadání chybějících dat
# ==============================================================================

class InputDialog(Toplevel):
    def __init__(self, parent, title=None, ask_po=False):
        super().__init__(parent)
        self.parent = parent
        self.transient(parent)
        if title: self.title(title)
        self.result = None
        self.body = tk.Frame(self)
        self.initial_focus = self.body_contents(self.body, ask_po)
        self.body.pack(padx=10, pady=10)
        self.buttonbox()
        self.grab_set()
        if not self.initial_focus: self.initial_focus = self
        self.protocol("WM_DELETE_WINDOW", self.cancel)
        self.geometry(f"+{parent.winfo_rootx()+50}+{parent.winfo_rooty()+50}")
        self.initial_focus.focus_set()
        self.wait_window(self)

    def body_contents(self, master, ask_po):
        Label(master, text="Datum dodání (DD.MM.RRRR):").grid(row=0, column=0, sticky="w", pady=2)
        Label(master, text="Číslo zákazníka:").grid(row=1, column=0, sticky="w", pady=2)
        self.entry_date = Entry(master, width=30)
        self.entry_customer = Entry(master, width=30)
        self.entry_date.insert(0, "06.06.2025")
        self.entry_customer.insert(0, "25555308")
        self.entry_date.grid(row=0, column=1, padx=5)
        self.entry_customer.grid(row=1, column=1, padx=5)

        self.ask_po = ask_po
        if self.ask_po:
            Label(master, text="Číslo obj. zákazníka (PO):").grid(row=2, column=0, sticky="w", pady=2)
            self.entry_po = Entry(master, width=30)
            self.entry_po.insert(0, "PO123456")
            self.entry_po.grid(row=2, column=1, padx=5)

        return self.entry_date

    def buttonbox(self):
        box = tk.Frame(self)
        Button(box, text="OK", width=10, command=self.ok, default=tk.ACTIVE).pack(side=tk.LEFT, padx=5, pady=5)
        Button(box, text="Zrušit", width=10, command=self.cancel).pack(side=tk.LEFT, padx=5, pady=5)
        self.bind("<Return>", self.ok)
        self.bind("<Escape>", self.cancel)
        box.pack(pady=10)

    def ok(self, event=None):
        date_val = self.entry_date.get()
        customer_val = self.entry_customer.get()
        po_val = self.entry_po.get() if self.ask_po else None

        if not re.match(r'^\d{2}\.\d{2}\.\d{4}$', date_val):
            messagebox.showerror("Chybný formát", "Datum musí být ve formátu DD.MM.RRRR.")
            return
        if not customer_val or customer_val in ["Číslo", "25555308"]:
            messagebox.showerror("Chybějící údaj", "Zadejte prosím platné číslo zákazníka.")
            return
        if self.ask_po and (not po_val or po_val in ["PO číslo", "PO123456"]):
            messagebox.showerror("Chybějící údaj", "Zadejte prosím číslo objednávky zákazníka (PO).")
            return

        self.result = (date_val, customer_val, po_val)
        self.withdraw()
        self.update_idletasks()
        self.parent.focus_set()
        self.destroy()

    def cancel(self, event=None):
        self.result = None
        self.parent.focus_set()
        self.destroy()

# ==============================================================================
# ČÁST 1: ZPRACOVÁNÍ DAT (JÁDRO PROGRAMU)
# ==============================================================================

def get_documents_folder():
    """Získá cestu ke složce Dokumenty pro aktuálního uživatele"""
    if os.name == 'nt':  # Windows
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                               r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders")
            documents_path = winreg.QueryValueEx(key, "Personal")[0]
            winreg.CloseKey(key)
            return documents_path
        except:
            return os.path.join(os.path.expanduser("~"), "Documents")
    else:
        return os.path.join(os.path.expanduser("~"), "Documents")

def load_excel_smart(path, sheet_index=0, description=""):
    """Inteligentně načte Excel soubor s kontrolou dostupných listů."""
    try:
        xl_file = pd.ExcelFile(path)
        sheet_names = xl_file.sheet_names
        print(f"Excel soubor '{description}' má {len(sheet_names)} listů: {sheet_names}")
        
        # Zkontrolujeme, jestli požadovaný list existuje
        if sheet_index >= len(sheet_names):
            print(f"Požadovaný list {sheet_index} neexistuje, používám první list: {sheet_names[0]}")
            sheet_index = 0
        
        sheet_to_use = sheet_names[sheet_index]
        print(f"Načítám list: {sheet_to_use}")
        
        return pd.read_excel(path, sheet_name=sheet_index)
    except Exception as e:
        raise ValueError(f"Chyba při načítání Excel souboru: {e}")

def load_data(path):
    """Načte data z CSV nebo Excel souboru."""
    try:
        if path.endswith('.csv'):
            return pd.read_csv(path, sep=';', encoding='windows-1250')
        elif path.endswith(('.xlsx', '.xls')):
            return load_excel_smart(path, 0, "obecný")
        else:
            raise ValueError("Nepodporovaný formát souboru. Použijte .csv, .xlsx, nebo .xls.")
    except Exception as e:
        raise ValueError(f"Chyba při načítání souboru: {e}")

def process_labara_order(order_path, dated_input, customer_no_input, po_input=None):
    """Zpracuje objednávku Labara s vestavěným katalogem."""
    print(f"--- Zpracovávám objednávku Labara: {os.path.basename(order_path)} ---")
    
    try:
        order_items_df = load_data(order_path)
        alde_catalog_df = get_embedded_catalog()  # POUŽÍVÁ VESTAVĚNÝ KATALOG!
        
        # Kontrola očekávaných sloupců pro LABARA
        expected_columns = ['číslo zboží', 'množství', 'název']
        missing_columns = [col for col in expected_columns if col not in order_items_df.columns]
        
        if missing_columns:
            print(f"Dostupné sloupce: {list(order_items_df.columns)}")
            messagebox.showwarning("Možná chybí sloupce", f"V souboru možná chybí očekávané sloupce pro LABARA:\n{missing_columns}\n\nDostupné sloupce:\n{list(order_items_df.columns)}\n\nPokračuji ve zpracování...")
            
    except Exception as e:
        messagebox.showerror("Chyba načítání", f"Chyba při načítání dat pro LABARA.\n\nDetail chyby: {e}")
        return pd.DataFrame()

    order_items_df.rename(columns={'číslo zboží': 'cislo_zbozi', 'množství': 'mnozstvi', 'název': 'nazev'}, inplace=True)
    order_items_df.reset_index(inplace=True)

    def get_robust_key(text):
        text = str(text)
        material_num_match = re.search(r'(\d{4})', text)
        thickness_match = re.search(r'přířez\s+([\d,]+)', text, re.IGNORECASE)
        if not thickness_match:
            thickness_match = re.search(r'tloušťka\s+([\d,]+)', text, re.IGNORECASE)
        if material_num_match and thickness_match:
            material_num = material_num_match.group(1)
            thickness = float(thickness_match.group(1).replace(',', '.'))
            return f"{material_num}-{thickness:.2f}"
        return None

    def get_catalog_key(text):
        text = str(text)
        material_num_match = re.search(r'(\d{4})', text)
        thickness_match = re.search(r'([\d,]+\.\d{2})', text.replace(',', '.'))
        if material_num_match and thickness_match:
            return f"{material_num_match.group(1)}-{thickness_match.group(1)}"
        return None

    order_items_df['robust_key'] = order_items_df['nazev'].apply(get_robust_key)
    alde_catalog_df['robust_key'] = alde_catalog_df['DESCRIPTION'].apply(get_catalog_key)
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'Popis Položky'}, inplace=True)

    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'Popis Položky', 'robust_key']], on='robust_key', how='left')
    final_df.drop_duplicates(subset=['index'], keep='first', inplace=True)

    unmatched_items = final_df[final_df['CATALOG_NO'].isnull()]
    if not unmatched_items.empty:
        unmatched_list = "\n".join(unmatched_items['nazev'].unique())
        messagebox.showwarning("Nenalezena shoda", f"Pro následující položky nebyla nalezena shoda v katalogu:\n\n{unmatched_list}")

    customer_po_no = os.path.splitext(os.path.basename(order_path))[0]
    final_df['mnozstvi'] = final_df['mnozstvi'].astype(str).str.replace(' ks', '', regex=False).str.strip().astype(int)

    def parse_dimensions(nazev):
        nazev = str(nazev)
        match_dims = re.search(r'-\s*([\d,.]+)\s*x\s*([\d,.]+)', nazev, re.IGNORECASE)
        if match_dims:
            return match_dims.group(1).strip().replace(',', '.'), match_dims.group(2).strip().replace(',', '.')
        return None, None

    dims_data = final_df['nazev'].apply(lambda x: pd.Series(parse_dimensions(x)))
    final_df[['W', 'L']] = dims_data

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': customer_no_input, 'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['mnozstvi'], 'DATED': pd.to_datetime(dated_input, format='%d.%m.%Y').strftime('%d.%m.%Y'),
        'PRICE': 0, 'CURRENCY_CODE': 'CZK', 'CUSTOMER_PO_NO': customer_po_no,
        'W': final_df['W'], 'L': final_df['L']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)
    return output_df

def process_impa_order(order_path, dated_input, customer_no_input, po_input):
    """Zpracuje objednávku IMPA s vestavěným katalogem."""
    print(f"--- Zpracovávám objednávku IMPA: {os.path.basename(order_path)} ---")
    
    try:
        order_items_df = load_data(order_path)
        alde_catalog_df = get_embedded_catalog()  # POUŽÍVÁ VESTAVĚNÝ KATALOG!
        
        # Kontrola očekávaných sloupců pro IMPA
        required_columns = ['Material', 'Hrubka', 'Sirka', 'Dlzka', 'Pocet kusov']
        missing_columns = [col for col in required_columns if col not in order_items_df.columns]
        
        if missing_columns:
            print(f"Dostupné sloupce: {list(order_items_df.columns)}")
            messagebox.showerror("Chybí sloupce", f"V souboru chybí očekávané sloupce pro IMPA:\n{missing_columns}\n\nDostupné sloupce:\n{list(order_items_df.columns)}")
            return pd.DataFrame()
            
    except Exception as e:
        messagebox.showerror("Chyba načítání", f"Chyba při načítání dat pro IMPA.\n\nDetail chyby: {e}")
        return pd.DataFrame()
        
    # Přejmenování sloupců pro IMPA formát
    order_items_df.rename(columns={
        'Material': 'material', 
        'Hrubka': 'hrubka', 
        'Sirka': 'W', 
        'Dlzka': 'L', 
        'Pocet kusov': 'QTY'
    }, inplace=True)
    order_items_df.reset_index(inplace=True)
    
    def create_keys(material_text, thickness_val):
        """Vytvoří přesný a volný klíč pro párování materiálů"""
        try:
            text = str(material_text).upper().replace(" ", "").replace("-", "")
            norm_match = re.search(r'(\d{4})', text)
            if not norm_match: 
                return None, None
            norm_part = norm_match.group(1)
            
            # Hledání temper kódu (T6, H14, atd.)
            temper_match = re.search(r'([TH]\d+)', text)
            temper_part = temper_match.group(1) if temper_match else ""
            
            thickness_float = float(str(thickness_val).replace(',', '.'))
            thickness_part = f"{thickness_float:.2f}"
            
            # Přesný klíč s temper kódem, volný bez
            precise_key = f"{norm_part}{temper_part}-{thickness_part}"
            loose_key = f"{norm_part}-{thickness_part}"
            
            return precise_key, loose_key
        except (ValueError, TypeError):
            return None, None

    # Vytvoření klíčů pro objednávku
    keys_df = order_items_df.apply(
        lambda row: pd.Series(create_keys(row['material'], row['hrubka']), index=['precise_key', 'loose_key']), 
        axis=1
    )
    order_items_df = pd.concat([order_items_df, keys_df], axis=1)

    # Příprava katalogu
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'Popis Položky'}, inplace=True)
    
    def get_thickness_from_desc(description):
        """Extrahuje tloušťku z popisu v katalogu"""
        desc_str = str(description)
        thickness_match = re.search(r'([\d,]+\.?\d+)(?:\s*mm)?\s*$', desc_str)
        return thickness_match.group(1) if thickness_match else None

    # Vytvoření klíčů pro katalog
    catalog_keys_df = alde_catalog_df.apply(
        lambda row: pd.Series(create_keys(row['Popis Položky'], get_thickness_from_desc(row['Popis Položky'])), index=['precise_key', 'loose_key']),
        axis=1
    )
    alde_catalog_df = pd.concat([alde_catalog_df, catalog_keys_df], axis=1)
    
    # Odebrání řádků bez klíčů
    order_items_df.dropna(subset=['precise_key', 'loose_key'], how='all', inplace=True)
    alde_catalog_df.dropna(subset=['precise_key', 'loose_key'], how='all', inplace=True)

    # Prvotní párování pomocí přesných klíčů
    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'Popis Položky', 'precise_key']], on='precise_key', how='left')
    
    # Druhé párování pomocí volných klíčů pro nenalezené položky
    unmatched_df = final_df[final_df['CATALOG_NO'].isnull()].copy()
    if not unmatched_df.empty:
        catalog_loose = alde_catalog_df[['CATALOG_NO', 'Popis Položky', 'loose_key']].dropna().drop_duplicates(subset=['loose_key'])
        loose_matches = pd.merge(
            unmatched_df.drop(columns=['CATALOG_NO', 'Popis Položky']), 
            catalog_loose, 
            on='loose_key', 
            how='left'
        )
        final_df.set_index('index', inplace=True)
        final_df.update(loose_matches.set_index('index'))
        final_df.reset_index(inplace=True)

    final_df.drop_duplicates(subset=['index'], keep='first', inplace=True)
    
    # Zobrazení nenalezených položek
    unmatched_items = final_df[final_df['CATALOG_NO'].isnull()]
    if not unmatched_items.empty:
        unmatched_list = "\n".join(unmatched_items['precise_key'].dropna().unique())
        messagebox.showwarning("Nenalezena shoda", f"I po volnějším hledání se pro následující klíče nepodařilo najít shodu:\n\n{unmatched_list}")

    # Vytvoření výstupního DataFrame
    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 
        'CUSTOMER_NO': customer_no_input, 
        'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['QTY'], 
        'DATED': pd.to_datetime(dated_input, format='%d.%m.%Y').strftime('%d.%m.%Y'),
        'PRICE': 0, 
        'CURRENCY_CODE': 'CZK', 
        'CUSTOMER_PO_NO': po_input,
        'W': final_df['W'], 
        'L': final_df['L']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)
    return output_df

def process_descon_order(order_path):
    """Zpracuje objednávku Descon s vestavěným katalogem."""
    print(f"--- Zpracovávám objednávku Descon: {os.path.basename(order_path)} ---")
    try:
        # DESCON obvykle používá druhý list (index 1), ale pokud neexistuje, použijeme první
        order_items_df = load_excel_smart(order_path, 1, "DESCON")
        
        # Odebereme posledních 3 řádky podle původní logiky
        if len(order_items_df) > 3:
            order_items_df = order_items_df.iloc[:-3]
            
        alde_catalog_df = get_embedded_catalog()  # POUŽÍVÁ VESTAVĚNÝ KATALOG!
        
        # Kontrola očekávaných sloupců
        required_columns = ['CisloDilu', 'MaterialVyroba', 'Tloustka', 'KS', 'Zakazka', 'SirkaPrumer', 'Delka']
        missing_columns = [col for col in required_columns if col not in order_items_df.columns]
        
        if missing_columns:
            print(f"Dostupné sloupce: {list(order_items_df.columns)}")
            messagebox.showerror("Chybí sloupce", f"V souboru chybí očekávané sloupce pro DESCON:\n{missing_columns}\n\nDostupné sloupce:\n{list(order_items_df.columns)}")
            return pd.DataFrame()
            
    except Exception as e:
        messagebox.showerror("Chyba načítání", f"Chyba při načítání dat pro Descon.\n\nDetail chyby: {e}")
        return pd.DataFrame()

    # Filtrujeme pouze řádky s CisloDilu obsahujícím '_'
    if 'CisloDilu' in order_items_df.columns:
        order_items_df = order_items_df[order_items_df['CisloDilu'].str.contains('_', na=False)]
    else:
        messagebox.showerror("Chybí sloupec", "Soubor neobsahuje sloupec 'CisloDilu' potřebný pro DESCON zpracování.")
        return pd.DataFrame()
        
    if len(order_items_df.columns) > 3:
        typ_column_name = order_items_df.columns[3]
    else:
        messagebox.showerror("Nedostatek sloupců", "Soubor nemá dostatek sloupců pro DESCON zpracování.")
        return pd.DataFrame()
        
    date_column_name = order_items_df.columns[-1]

    def create_search_key(row):
        material = row['MaterialVyroba']; typ = row[typ_column_name]; thickness = row['Tloustka']
        base_material = ""
        if "5083" in material: base_material = "5083"
        elif "6082" in material: base_material = "ENAW6082T651"
        if "frézovaná" in str(typ): base_material += " litá frézovaná"
        elif "litá" in str(typ): base_material += " litá"
        if not base_material or pd.isna(thickness): return None
        return f"{base_material} {float(thickness):.2f}".replace('.', ',')

    order_items_df['search_key'] = order_items_df.apply(create_search_key, axis=1)
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'search_key'}, inplace=True)
    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'search_key']], on='search_key', how='left')

    # Zobrazení nenalezených položek
    unmatched_items = final_df[final_df['CATALOG_NO'].isnull()]
    if not unmatched_items.empty:
        unmatched_list = "\n".join(unmatched_items['search_key'].dropna().unique())
        messagebox.showwarning("Nenalezena shoda", f"Pro následující klíče nebyla nalezena shoda v katalogu:\n\n{unmatched_list}")

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': '505992', 'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['KS'], 'DATED': pd.to_datetime(final_df[date_column_name].iloc[0]).strftime('%d.%m.%Y'),
        'PRICE': 0, 'CURRENCY_CODE': 'CZK', 'CUSTOMER_PO_NO': final_df['Zakazka'],
        'W': final_df['SirkaPrumer'], 'L': final_df['Delka']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)
    return output_df

# ==============================================================================
# ČÁST 2: GRAFICKÉ ROZHRANÍ (GUI)
# ==============================================================================

def run_processing(root, process_func, title, filetypes, ask_po=False):
    """Univerzální funkce pro spuštění zpracování objednávek"""
    filepath = filedialog.askopenfilename(title=title, filetypes=filetypes)
    if not filepath: 
        return

    # Dialog pro zadání chybějících údajů
    dialog = InputDialog(root, "Doplňte chybějící údaje", ask_po=ask_po)
    if not dialog.result: 
        print("Zpracování zrušeno uživatelem.")
        return
    
    dated, customer_no, po_no = dialog.result

    try:
        # ŽÁDNÝ EXTERNÍ KATALOG POTŘEBA!
        
        # Zpracování podle typu objednávky
        po_for_filename = po_no
        
        if process_func == process_labara_order:
            output_df = process_func(filepath, dated, customer_no)
            po_for_filename = os.path.splitext(os.path.basename(filepath))[0]
        elif process_func == process_descon_order:
            output_df = process_func(filepath)
            if output_df.empty: 
                return
            po_for_filename = "descon_output"
        else:  # IMPA
            output_df = process_func(filepath, dated, customer_no, po_no)

        if output_df.empty:
            messagebox.showwarning("Prázdný výstup", "Po zpracování nebyla nalezena žádná platná data k uložení.")
            return
        
        # Uložení výstupu
        safe_filename = re.sub(r'[\\/*?:"<>|]', "", str(po_for_filename))
        
        # Vytvoření výstupní složky v Documents
        documents_folder = get_documents_folder()
        output_folder = os.path.join(documents_folder, 'Migrace_Vystupy')
        if not os.path.exists(output_folder): 
            os.makedirs(output_folder)
        
        output_filename = f"vystup_{safe_filename}.csv"
        output_path = os.path.join(output_folder, output_filename)
        
        # Uložení s UTF-8 BOM pro správné zobrazení českých znaků
        output_df.to_csv(output_path, index=False, sep=';', encoding='utf-8-sig')
        messagebox.showinfo("Hotovo", f"Zpracování dokončeno!\n\nVýstup byl uložen do:\n{output_path}\n\n🎯 POUŽÍVÁ VESTAVĚNÝ KATALOG ({len(EMBEDDED_ALDE_CATALOG)} položek)")

    except Exception as e:
        messagebox.showerror("Chyba při zpracování", f"Vyskytla se neočekávaná chyba:\n\n{e}")
        import traceback
        traceback.print_exc()

# ==============================================================================
# ČÁST 3: HLAVNÍ PROGRAM
# ==============================================================================

def main():
    """Hlavní funkce aplikace"""
    root = tk.Tk()
    root.title("Nástroj pro migraci objednávek v8.0 - STANDALONE EDITION")
    root.geometry("550x380")
    root.resizable(False, False)
    
    # Hlavní frame
    main_frame = tk.Frame(root, padx=20, pady=20)
    main_frame.pack(expand=True, fill=tk.BOTH)
    
    # Nadpis
    label = tk.Label(main_frame, text="Vyberte typ objednávky ke zpracování:", font=("Segoe UI", 12, "bold"))
    label.pack(pady=(0, 15))

    # Info o vestavěném katalogu
    info_catalog = tk.Label(main_frame, text=f"🎯 STANDALONE VERZE - VESTAVĚNÝ KATALOG ({len(EMBEDDED_ALDE_CATALOG)} položek)", 
                           font=("Segoe UI", 10, "bold"), fg="#2E7D32", bg="#E8F5E8", relief="raised", bd=1)
    info_catalog.pack(pady=(0, 15), fill="x")

    # Výhody
    advantages = tk.Label(main_frame, text="✅ Žádné externí soubory  ✅ Rychlé spuštění  ✅ Portable", 
                         font=("Segoe UI", 9), fg="#1565C0")
    advantages.pack(pady=(0, 15))

    # Definice typů souborů
    filetypes_all = [("Podporované soubory", "*.csv *.xlsx *.xls"), ("Všechny soubory", "*.*")]
    filetypes_excel = [("Excel soubory", "*.xlsx *.xls"), ("Všechny soubory", "*.*")]

    # Tlačítko pro LABARA
    btn_labara = tk.Button(main_frame, text="Zpracovat objednávku LABARA", 
        command=lambda: run_processing(root, process_labara_order, "Vyberte objednávku LABARA", filetypes_all), 
        width=50, height=2, bg='#E3F2FD', activebackground='#BBDEFB')
    btn_labara.pack(pady=8)
    
    # Tlačítko pro IMPA
    btn_impa = tk.Button(main_frame, text="Zpracovat objednávku IMPA", 
        command=lambda: run_processing(root, process_impa_order, "Vyberte objednávku IMPA", filetypes_excel, ask_po=True), 
        width=50, height=2, bg='#D4EDDA', activebackground='#C3E6CB')
    btn_impa.pack(pady=8)

    # Tlačítko pro DESCON
    btn_descon = tk.Button(main_frame, text="Zpracovat objednávku DESCON", 
        command=lambda: run_processing(root, process_descon_order, "Vyberte objednávku DESCON", filetypes_excel), 
        width=50, height=2, bg='#FFF3CD', activebackground='#FFEAA7')
    btn_descon.pack(pady=8)
    
    # Informační texty
    info_label = tk.Label(main_frame, text="IMPA: Požaduje PO číslo\nLABARA: Používá název souboru jako PO", 
                         font=("Segoe UI", 9), fg="gray")
    info_label.pack(pady=(15, 0))
    
    root.mainloop()

if __name__ == "__main__":
    main()