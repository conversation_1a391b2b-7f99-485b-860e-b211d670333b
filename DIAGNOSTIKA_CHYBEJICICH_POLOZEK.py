#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIKA CHYBĚJÍCÍCH POLOŽEK V LABARA MIGRACI
===============================================
Tento script pomůže identifikovat, proč některé položky chybí ve výstupu
"""

import pandas as pd
import re
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_standalone_final import get_embedded_catalog, load_data
    print("✅ Modul úspěšně načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def analyze_labara_file(file_path):
    """Analyzuje LABARA soubor a identifikuje problémov<PERSON>."""
    print(f"\n🔍 ANALÝZA SOUBORU: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        # Načtení dat
        df = load_data(file_path)
        catalog_df = get_embedded_catalog()
        
        print(f"📊 Celkem řádků v souboru: {len(df)}")
        print(f"📚 Položek v katalogu: {len(catalog_df)}")
        print(f"🔤 Sloupce v souboru: {list(df.columns)}")
        
        # Flexibilní mapování sloupců
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower()
            if 'číslo' in col_lower or 'cislo' in col_lower or 'kod' in col_lower:
                column_mapping[col] = 'cislo_zbozi'
            elif 'množství' in col_lower or 'mnozstvi' in col_lower or 'qty' in col_lower or 'ks' in col_lower:
                column_mapping[col] = 'mnozstvi'
            elif 'název' in col_lower or 'nazev' in col_lower or 'popis' in col_lower or 'description' in col_lower:
                column_mapping[col] = 'nazev'
        
        print(f"🔄 Mapování sloupců: {column_mapping}")
        df.rename(columns=column_mapping, inplace=True)
        
        if 'nazev' not in df.columns:
            print("❌ Nepodařilo se najít sloupec s názvy produktů!")
            return
        
        print(f"\n📋 UKÁZKA DAT (prvních 5 řádků):")
        print(df[['nazev', 'mnozstvi'] if 'mnozstvi' in df.columns else ['nazev']].head().to_string(index=False))
        
        # Analýza každé položky
        print(f"\n🔍 DETAILNÍ ANALÝZA POLOŽEK:")
        print("-" * 60)
        
        successful_matches = 0
        failed_matches = []
        
        for idx, row in df.iterrows():
            nazev = str(row['nazev'])
            print(f"\n[{idx+1}] Analyzuji: '{nazev}'")
            
            # Zkusíme vytvořit klíč
            key = create_robust_key(nazev)
            if key:
                print(f"    ✅ Klíč vytvořen: {key}")
                
                # Zkusíme najít v katalogu
                catalog_matches = find_catalog_matches(key, catalog_df)
                if catalog_matches:
                    print(f"    ✅ Nalezeno v katalogu: {len(catalog_matches)} shod")
                    for match in catalog_matches[:3]:  # Max 3 shody
                        print(f"        → {match}")
                    successful_matches += 1
                else:
                    print(f"    ❌ Nenalezeno v katalogu")
                    failed_matches.append((nazev, key))
                    
                    # Zkusíme najít podobné položky
                    similar = find_similar_items(key, catalog_df)
                    if similar:
                        print(f"    💡 Podobné položky v katalogu:")
                        for sim in similar[:3]:
                            print(f"        → {sim}")
            else:
                print(f"    ❌ Nepodařilo se vytvořit klíč")
                failed_matches.append((nazev, None))
        
        # Shrnutí
        print(f"\n📊 SHRNUTÍ ANALÝZY:")
        print("=" * 60)
        print(f"✅ Úspěšně namapováno: {successful_matches}/{len(df)} ({successful_matches/len(df)*100:.1f}%)")
        print(f"❌ Nenalezeno: {len(failed_matches)}/{len(df)} ({len(failed_matches)/len(df)*100:.1f}%)")
        
        if failed_matches:
            print(f"\n❌ PROBLÉMOVÉ POLOŽKY:")
            for i, (nazev, key) in enumerate(failed_matches[:10], 1):  # Max 10
                print(f"{i:2d}. '{nazev}' → klíč: {key if key else 'NELZE VYTVOŘIT'}")
        
        return successful_matches, len(df), failed_matches
        
    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")
        import traceback
        traceback.print_exc()

def create_robust_key(text):
    """Vytvoří robustní klíč z názvu produktu."""
    text = str(text)
    
    # Hledáme materiál
    material_match = re.search(r'(\d{4}|ENAW\d+\w*)', text)
    
    # Hledáme tloušťku
    thickness_match = None
    
    # 1. Přířez + číslo
    thickness_match = re.search(r'přířez\s+([\d,]+(?:\.?\d*)?)', text, re.IGNORECASE)
    
    # 2. Tloušťka + číslo  
    if not thickness_match:
        thickness_match = re.search(r'tloušťka\s+([\d,]+(?:\.?\d*)?)', text, re.IGNORECASE)
    
    # 3. Číslo + mm
    if not thickness_match:
        thickness_match = re.search(r'(\d+[,.]?\d*)\s*mm', text, re.IGNORECASE)
    
    # 4. Číslo s desetinnou částí (ale ne rozměry)
    if not thickness_match:
        matches = re.findall(r'(\d+[,.]?\d+)(?!\s*x)', text, re.IGNORECASE)
        if matches:
            for match in matches:
                num = float(match.replace(',', '.'))
                if 1 <= num <= 500:  # Rozumný rozsah
                    thickness_match = type('Match', (), {'group': lambda self, x: match})()
                    break
            
    if material_match and thickness_match:
        material = material_match.group(1)
        thickness = float(thickness_match.group(1).replace(',', '.'))
        
        # Normalizace materiálu
        if material == "6061":
            material = "ENAW6061T651"
        elif material == "7075":
            material = "ENAW7075"
        elif material == "5754":
            material = "ENAW5754H111"
        
        return f"{material}-{thickness:.2f}"
    return None

def find_catalog_matches(key, catalog_df):
    """Najde přesné shody v katalogu."""
    catalog_keys = []
    for _, row in catalog_df.iterrows():
        desc = str(row['DESCRIPTION'])
        cat_key = create_catalog_key(desc)
        if cat_key == key:
            catalog_keys.append(f"{row['PART_NO']} - {desc}")
    return catalog_keys

def find_similar_items(key, catalog_df):
    """Najde podobné položky v katalogu."""
    if not key or '-' not in key:
        return []
    
    material, thickness = key.split('-', 1)
    similar = []
    
    for _, row in catalog_df.iterrows():
        desc = str(row['DESCRIPTION'])
        if material.replace('ENAW', '') in desc or material in desc:
            similar.append(f"{row['PART_NO']} - {desc}")
    
    return similar[:5]  # Max 5 podobných

def create_catalog_key(text):
    """Vytvoří klíč z popisu v katalogu."""
    text = str(text)
    material_match = re.search(r'(\d{4}|ENAW\d+\w*|UNIDAL|WELDURAL)', text)
    thickness_match = re.search(r'(\d+[,.]?\d*)\s*$', text)
    
    if material_match and thickness_match:
        material = material_match.group(1)
        thickness = thickness_match.group(1).replace(',', '.')
        return f"{material}-{thickness}"
    return None

def main():
    print("🔍 DIAGNOSTIKA CHYBĚJÍCÍCH POLOŽEK V LABARA MIGRACI")
    print("=" * 60)
    
    # Požádáme uživatele o cestu k souboru
    print("\n📁 Zadejte cestu k LABARA souboru pro analýzu:")
    print("   (nebo stiskněte Enter pro ukončení)")
    
    file_path = input("Cesta k souboru: ").strip().strip('"')
    
    if not file_path:
        print("Ukončuji...")
        return
    
    if not os.path.exists(file_path):
        print(f"❌ Soubor neexistuje: {file_path}")
        return
    
    # Spustíme analýzu
    result = analyze_labara_file(file_path)
    
    if result:
        successful, total, failed = result
        
        print(f"\n💡 DOPORUČENÍ:")
        print("-" * 60)
        
        if successful == total:
            print("✅ Všechny položky by měly být správně zpracovány!")
        elif successful > total * 0.8:
            print("⚠️ Většina položek je OK, ale některé chybí:")
            print("   • Zkontrolujte názvy problémových položek")
            print("   • Možná nejsou v katalogu dostupné")
        else:
            print("❌ Mnoho položek se nezpracovává:")
            print("   • Zkontrolujte formát názvů produktů")
            print("   • Možná je potřeba upravit mapovací logiku")
    
    input("\nStiskněte Enter pro ukončení...")

if __name__ == "__main__":
    main()
