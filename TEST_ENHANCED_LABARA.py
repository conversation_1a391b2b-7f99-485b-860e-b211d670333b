#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST VYLEPŠENÉ LABARA DIAGNOSTIKY
=================================
Testuje nové diagnostické funkce pro LABARA zpracování
"""

import pandas as pd
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_standalone_final import process_labara_order
    print("✅ Modul úspěšně načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def create_test_labara_files():
    """Vytvoří testovací LABARA soubory - CSV i Excel."""
    
    # Testovací data s různými formáty
    test_data = {
        'číslo zboží': ['001', '002', '003', '004', '005', '006'],
        'množství': ['5 ks', '10 ks', '2 ks', '1 ks', '3 ks', '4 ks'],
        'název': [
            '5083 litá přířez 10,00 - 80 x 90',           # ✅ Mělo by fungovat
            '5083 litá frézovaná tloušťka 15,00 - 150 x 300',  # ✅ Mělo by fungovat
            '6061T651 válcovaná 25,00 - 210 x 570',       # ✅ Mělo by fungovat
            'ENAW5754H111 30,00 - 65 x 135',             # ✅ Mělo by fungovat
            'Neznámý materiál XYZ123 50,00',             # ❌ Nemělo by fungovat
            '5083 litá bez tloušťky - 100 x 200'         # ❌ Nemělo by fungovat
        ]
    }
    
    df = pd.DataFrame(test_data)
    
    # CSV soubor
    csv_file = 'test_enhanced_labara.csv'
    df.to_csv(csv_file, index=False, sep=';', encoding='windows-1250')
    print(f"✅ Vytvořen CSV: {csv_file}")
    
    # Excel soubor
    excel_file = 'test_enhanced_labara.xlsx'
    df.to_excel(excel_file, index=False)
    print(f"✅ Vytvořen Excel: {excel_file}")
    
    return csv_file, excel_file

def test_enhanced_processing(file_path, file_type):
    """Testuje vylepšené zpracování."""
    print(f"\n{'='*60}")
    print(f"🧪 TESTOVÁNÍ {file_type.upper()}: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    try:
        # Test zpracování s detailní diagnostikou
        result_df = process_labara_order(
            order_path=file_path,
            dated_input="01.08.2025", 
            customer_no_input="504398"
        )
        
        print(f"\n✅ VÝSLEDEK ZPRACOVÁNÍ:")
        print(f"   📊 Zpracováno: {len(result_df)} položek")
        
        if len(result_df) > 0:
            print(f"\n📋 VÝSTUPNÍ DATA:")
            print(result_df.to_string(index=False))
            
            # Uložení výsledku
            output_file = f'test_enhanced_output_{file_type}.csv'
            result_df.to_csv(output_file, index=False, sep=';', encoding='utf-8-sig')
            print(f"\n💾 Výstup uložen do: {output_file}")
        else:
            print("   ❌ ŽÁDNÉ PLATNÉ POLOŽKY!")
            
    except Exception as e:
        print(f"❌ CHYBA PŘI ZPRACOVÁNÍ: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🔍 TEST VYLEPŠENÉ LABARA DIAGNOSTIKY")
    print("="*60)
    print("Tento test ověří nové diagnostické funkce:")
    print("• Detailní logy pro každou položku")
    print("• Statistiky úspěšnosti mapování")
    print("• Zobrazení chybějících položek")
    print("• Podporu CSV i Excel formátů")
    print("="*60)
    
    # Vytvoření testovacích souborů
    print("\n📁 VYTVÁŘENÍ TESTOVACÍCH SOUBORŮ:")
    csv_file, excel_file = create_test_labara_files()
    
    try:
        # Test CSV souboru
        test_enhanced_processing(csv_file, "csv")
        
        # Test Excel souboru
        test_enhanced_processing(excel_file, "excel")
        
        print(f"\n{'='*60}")
        print("✅ TESTY DOKONČENY")
        print("="*60)
        print("\n💡 CO BYSTE MĚLI VIDĚT:")
        print("• Detailní logy pro každou položku")
        print("• Klíče vytvořené z názvů produktů")
        print("• Statistiky úspěšnosti mapování")
        print("• Seznam nenalezených položek")
        print("• Návrhy podobných položek z katalogu")
        print("• Finální statistiky zpracování")
        
    finally:
        # Úklid
        for file in [csv_file, excel_file]:
            if os.path.exists(file):
                os.remove(file)
                print(f"🧹 Smazán: {file}")
    
    input("\nStiskněte Enter pro ukončení...")

if __name__ == "__main__":
    main()
