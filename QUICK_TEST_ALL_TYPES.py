#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RYCHLÝ TEST VŠECH TYPŮ
=====================
Otestuje LABARA, IMPA a DESCON a ukáže přesně, kde je problém
"""

import pandas as pd
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_final_all_fixed import process_labara_order_fixed, process_impa_order_fixed, process_descon_order_fixed
    print("✅ Modul úspěšně načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def test_all_types():
    """Test všech tří typů."""
    print("🧪 RYCHLÝ TEST VŠECH TYPŮ")
    print("=" * 60)
    
    # Test 1: LABARA
    print("\n📋 TEST 1: LABARA")
    print("-" * 30)
    
    labara_data = {
        'nazev': [
            '5083 litá přířez 10,00 - 80 x 90',
            'ENAW6061T651 válcovaná 25,00 - 200 x 400',
            '7075 tloušťka 15,00 mm'
        ],
        'mnozstvi': [2, 3, 1]
    }
    
    df = pd.DataFrame(labara_data)
    test_file = 'test_labara.csv'
    df.to_csv(test_file, index=False, sep=';', encoding='windows-1250')
    print(f"✅ Vytvořen testovací LABARA soubor: {test_file}")
    
    try:
        print(f"\n🚀 SPOUŠTÍM LABARA TEST:")
        result_df = process_labara_order_fixed(
            test_file,
            "01.08.2025", 
            "504398"
        )
        
        print(f"\n📊 VÝSLEDEK LABARA:")
        print(f"   Zpracováno: {len(result_df)} položek")
        
        if len(result_df) > 0:
            print("✅ LABARA FUNGUJE!")
        else:
            print("❌ LABARA NEFUNGUJE!")
            
    except Exception as e:
        print(f"❌ CHYBA LABARA: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if os.path.exists(test_file):
            os.remove(test_file)
    
    # Test 2: IMPA
    print(f"\n📋 TEST 2: IMPA")
    print("-" * 30)
    
    impa_data = {
        'Material': ['5083', '6061T651', '5754'],
        'Hrubka': [10.0, 25.0, 30.0],
        'Sirka': [100, 200, 150],
        'Dlzka': [200, 400, 300],
        'Pocet kusov': [2, 3, 1]
    }
    
    df = pd.DataFrame(impa_data)
    test_file = 'test_impa.xlsx'
    df.to_excel(test_file, index=False)
    print(f"✅ Vytvořen testovací IMPA soubor: {test_file}")
    
    try:
        print(f"\n🚀 SPOUŠTÍM IMPA TEST:")
        result_df = process_impa_order_fixed(
            test_file,
            "01.08.2025", 
            "504398",
            "IMPA_TEST_2025"
        )
        
        print(f"\n📊 VÝSLEDEK IMPA:")
        print(f"   Zpracováno: {len(result_df)} položek")
        
        if len(result_df) > 0:
            print("✅ IMPA FUNGUJE!")
        else:
            print("❌ IMPA NEFUNGUJE!")
            
    except Exception as e:
        print(f"❌ CHYBA IMPA: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if os.path.exists(test_file):
            os.remove(test_file)
    
    # Test 3: DESCON
    print(f"\n📋 TEST 3: DESCON")
    print("-" * 30)
    
    descon_data = {
        'nazev': [
            '5083 litá přířez 12,00 - 100 x 200',
            'ENAW6061T651 válcovaná 8,00 - 150 x 300',
            '5754 tloušťka 35,00 mm',
            'Prázdný řádek 1',  # Tyto budou odstraněny
            'Prázdný řádek 2',
            'Prázdný řádek 3'
        ],
        'mnozstvi': [1, 2, 1, 0, 0, 0]
    }
    
    df = pd.DataFrame(descon_data)
    test_file = 'test_descon.xlsx'
    df.to_excel(test_file, index=False)
    print(f"✅ Vytvořen testovací DESCON soubor: {test_file}")
    
    try:
        print(f"\n🚀 SPOUŠTÍM DESCON TEST:")
        result_df = process_descon_order_fixed(test_file)
        
        print(f"\n📊 VÝSLEDEK DESCON:")
        print(f"   Zpracováno: {len(result_df)} položek")
        
        if len(result_df) > 0:
            print("✅ DESCON FUNGUJE!")
        else:
            print("❌ DESCON NEFUNGUJE!")
            
    except Exception as e:
        print(f"❌ CHYBA DESCON: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if os.path.exists(test_file):
            os.remove(test_file)
    
    print(f"\n🎯 SHRNUTÍ TESTŮ:")
    print("=" * 60)
    print("Zkontrolujte výše uvedené výsledky:")
    print("✅ = typ funguje")
    print("❌ = typ nefunguje")
    print("")
    print("Pokud IMPA nebo DESCON nefungují, zkontrolujte chybové zprávy výše.")

if __name__ == "__main__":
    test_all_types()
    input("\nStiskněte Enter pro ukončení...")
