#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST OPRAVENÉ IMPA FUNKCE
========================
Rychlý test pro ověření, že IMPA opravy fungují
"""

import pandas as pd
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_standalone_final import process_impa_order
    print("✅ Modul úspěšně načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def test_impa_fixed():
    """Test opravené IMPA funkce."""
    print("🧪 TEST OPRAVENÉ IMPA FUNKCE")
    print("=" * 50)
    
    # Vytvoříme testovací IMPA soubor
    test_data = {
        'Material': ['5083', '6061T651', '5754', '7075'],
        'Hrubka': [10.0, 25.0, 30.0, 15.0],
        'Sirka': [100, 200, 150, 180],
        'Dlzka': [200, 400, 300, 350],
        'Pocet kusov': [2, 3, 1, 4]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_impa_fixed.xlsx'
    df.to_excel(test_file, index=False)
    print(f"✅ Vytvořen testovací soubor: {test_file}")
    
    try:
        print(f"\n🚀 SPOUŠTÍM IMPA ZPRACOVÁNÍ:")
        print("-" * 30)
        
        result_df = process_impa_order(
            order_path=test_file,
            dated_input="01.08.2025", 
            customer_no_input="504398",
            po_input="IMPA_FIXED_TEST"
        )
        
        print(f"\n✅ VÝSLEDEK:")
        print(f"   📊 Zpracováno: {len(result_df)} položek")
        
        if len(result_df) > 0:
            print(f"\n📋 VÝSTUPNÍ DATA:")
            print(result_df.to_string(index=False))
            
            # Uložení výsledku
            output_file = 'test_impa_fixed_output.csv'
            result_df.to_csv(output_file, index=False, sep=';', encoding='utf-8-sig')
            print(f"\n💾 Výstup uložen do: {output_file}")
            
            print(f"\n🎯 ÚSPĚCH! IMPA NYNÍ FUNGUJE!")
        else:
            print("   ❌ STÁLE ŽÁDNÉ PLATNÉ POLOŽKY!")
            print("   💡 Zkontrolujte logy výše pro identifikaci problému")
            
    except Exception as e:
        print(f"❌ CHYBA: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Úklid
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 Smazán testovací soubor: {test_file}")

if __name__ == "__main__":
    test_impa_fixed()
    input("\nStiskněte Enter pro ukončení...")
