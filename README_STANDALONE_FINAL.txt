MIGRACE OBJEDNAVEK v7.1 - FINAL STANDALONE EDITION
==================================================

🎯 KOMPLETNÍ WINDOWS 64-BIT ŘEŠENÍ - VŠE V JEDNOM SOUBORU!

Tento balíček obsahuje finální standalone řešení pro migraci objednávek.
Jednoduše spusťte EXE a máte vše potřebné!

📦 OBSAH BALÍČKU:

🚀 HLAVNÍ SOUBORY:
• migrace_standalone_final.py     - Kompletní Python aplikace
• Migrace_Objednavek_Final.exe    - Standalone Windows EXE (po buildu)
• BUILD_STANDALONE_EXE.bat        - Builder pro vytvoření EXE
• TEST_STANDALONE.bat             - Test aplikace před buildem

📊 TESTOVACÍ DATA:
• IMPA_Test_Data/                 - IMPA1, IMPA2, IMPA3 testovací soubory

═══════════════════════════════════════════════════════════════

🎯 RYCHLÉ SPUŠTĚNÍ (DOPORUČENO):

MOŽNOST A - Použít hotový EXE (pokud existuje):
1. Spusťte: Migrace_Objednavek_Final.exe
2. Vyberte typ objednávky (LABARA/IMPA/DESCON)
3. Vyberte vstupní soubor
4. Vyplňte požadované údaje
5. Hotovo!

MOŽNOST B - Vytvořit vlastní EXE:
1. Spusťte: BUILD_STANDALONE_EXE.bat
2. Počkejte na dokončení (3-5 minut)
3. Spusťte: Migrace_Objednavek_Final.exe

MOŽNOST C - Testovat před buildem:
1. Spusťte: TEST_STANDALONE.bat
2. Otestujte funkcionalitu
3. Poté použijte BUILD_STANDALONE_EXE.bat

═══════════════════════════════════════════════════════════════

🔍 JAK TO FUNGUJE:

STANDALONE VÝHODY:
✅ Vestavěný ALDE katalog (508 položek)
✅ Žádné externí soubory potřeba
✅ Funguje na jakémkoliv Windows PC
✅ Žádná instalace Python potřeba
✅ Kompletně portable
✅ Optimalizováno pro Windows 64-bit

PODPOROVANÉ FORMÁTY:
• LABARA (CSV) - automatické PO podle názvu souboru
• IMPA (Excel) - vyžaduje zadání PO čísla
• DESCON (Excel) - automatické datum a zákazník

VÝSTUP:
• CSV soubory s UTF-8 BOM kódováním
• Automatické uložení do ~/Documents/Migrace_Vystupy/
• Standardizovaný formát pro další zpracování

═══════════════════════════════════════════════════════════════

📊 POUŽITÍ APLIKACE:

1. Spusťte aplikaci (EXE nebo Python script)
2. Vyberte typ objednávky:
   📄 LABARA - CSV soubory
   📊 IMPA - Excel soubory (vyžaduje PO číslo)
   🏭 DESCON - Excel soubory (automatické)
3. Vyberte vstupní soubor s objednávkou
4. Pro LABARA/IMPA zadejte:
   • Datum dodání (DD.MM.RRRR)
   • Číslo zákazníka
   • PO číslo (pouze IMPA)
5. Výstup se automaticky uloží do:
   ~/Documents/Migrace_Vystupy/

═══════════════════════════════════════════════════════════════

🧪 TESTOVÁNÍ:

Použijte testovací soubory z IMPA_Test_Data/:
• IMPA1.xlsx - testovací objednávka typ 1
• IMPA2.xlsx - testovací objednávka typ 2
• IMPA3.xlsx - testovací objednávka typ 3

Aplikace by měla zpracovat testovací data a vytvořit výstupní CSV soubory.

═══════════════════════════════════════════════════════════════

🛠️ ŘEŠENÍ PROBLÉMŮ:

"EXE se nespustí":
→ Spusťte jako administrator
→ Zkontrolujte Windows Defender
→ Klikněte "Další informace" → "Přesto spustit"

"Python script nefunguje":
→ Spusťte TEST_STANDALONE.bat
→ Zkontrolujte instalaci Python
→ Zkontrolujte závislosti (pandas, openpyxl)

"Build EXE selhal":
→ Zkontrolujte Python instalaci
→ Zkontrolujte internetové připojení
→ Spusťte jako administrator

"Aplikace 'problikne'":
→ Spusťte z Command Prompt pro zobrazení chyb
→ Zkontrolujte testovací data
→ Zkuste jiný vstupní soubor

"Chybí výstupní soubory":
→ Zkontrolujte ~/Documents/Migrace_Vystupy/
→ Zkontrolujte oprávnění k zápisu
→ Zkuste spustit jako administrator

═══════════════════════════════════════════════════════════════

💡 PRO VÝVOJÁŘE:

STRUKTURA KÓDU:
• Vestavěný katalog jako Python list
• Univerzální načítání CSV/Excel souborů
• Inteligentní mapování produktů
• Parsování rozměrů z názvů
• UTF-8 BOM výstup pro české znaky

CUSTOMIZACE:
• Katalog lze rozšířit v EMBEDDED_ALDE_CATALOG
• Mapovací logiku lze upravit v process_* funkcích
• GUI lze přizpůsobit v create_main_window()

BUILD PROCES:
• PyInstaller s --onefile --windowed
• Všechny závislosti zabaleny
• Optimalizováno pro velikost a rychlost

═══════════════════════════════════════════════════════════════

🎉 VÝHODY TOHOTO ŘEŠENÍ:

✅ 100% standalone - vše v jednom EXE
✅ Vestavěný katalog - žádné externí soubory
✅ Optimalizované GUI s moderním designem
✅ Inteligentní error handling
✅ Automatická detekce formátů
✅ Podpora českých znaků
✅ Portable - funguje z USB/síťové složky
✅ Rychlé spuštění - žádné čekání na načítání
✅ Kompletní diagnostické nástroje
✅ Testovací data zahrnuta

═══════════════════════════════════════════════════════════════

📋 TECHNICKÉ DETAILY:

• Python: 3.8+ kompatibilní
• Knihovny: pandas, openpyxl, tkinter
• GUI: tkinter (nativní Windows)
• Výstup: CSV s UTF-8 BOM kódováním
• Velikost EXE: ~15-25 MB (závisí na build)
• Kompatibilita: Windows 7-11, x64
• Katalog: 508 ALDE položek (vestavěný)

═══════════════════════════════════════════════════════════════

Vytvořeno: 03.07.2025
Verze: Final Standalone Edition v7.1
Řeší: Kompletní standalone řešení pro Windows 64-bit

© 2025 - Migrace Objednávek - Final Standalone Solution
