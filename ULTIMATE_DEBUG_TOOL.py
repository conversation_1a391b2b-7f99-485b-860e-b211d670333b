#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTIMATE DEBUG TOOL - NAJDE PŘESNĚ, PROČ NEJSOU ŽÁDNÁ DATA
===========================================================
Tento nástroj ti ukáže přesně, co se děje s tvými daty
"""

import pandas as pd
import os
import sys
import re

# Přidáme cestu k našemu modulu
sys.path.append('.')

def ultimate_debug():
    """Ultimátní diagnostika - najde přesně problém."""
    print("🔍 ULTIMATE DEBUG TOOL")
    print("=" * 60)
    print("Tento nástroj najde přesně, proč nejsou žádná platná data!")
    print("=" * 60)
    
    # Požádáme o cestu k souboru
    print("\n📁 Zadejte cestu k vašemu souboru (LABARA nebo IMPA):")
    file_path = input("Cesta: ").strip().strip('"')
    
    if not file_path or not os.path.exists(file_path):
        print(f"❌ Soubor neexistuje: {file_path}")
        return
    
    # Určíme typ souboru
    print(f"\n🔍 URČOVÁNÍ TYPU SOUBORU:")
    file_type = None
    if "labara" in file_path.lower() or file_path.lower().endswith('.csv'):
        file_type = "LABARA"
    elif "impa" in file_path.lower() or file_path.lower().endswith(('.xlsx', '.xls')):
        file_type = "IMPA"
    else:
        print("❓ Typ souboru není jasný. Jaký je to typ?")
        print("1 - LABARA (CSV)")
        print("2 - IMPA (Excel)")
        choice = input("Vyberte (1/2): ").strip()
        file_type = "LABARA" if choice == "1" else "IMPA"
    
    print(f"✅ Detekován typ: {file_type}")
    
    try:
        # KROK 1: Načtení souboru
        print(f"\n📂 KROK 1: NAČÍTÁNÍ SOUBORU")
        print("-" * 40)
        
        if file_path.lower().endswith('.csv'):
            # CSV - zkusíme různá kódování
            df = None
            for encoding in ['windows-1250', 'utf-8', 'cp1252', 'iso-8859-2']:
                try:
                    for sep in [';', ',', '\t']:
                        try:
                            test_df = pd.read_csv(file_path, encoding=encoding, sep=sep)
                            if len(test_df.columns) > 1:
                                df = test_df
                                print(f"✅ Úspěšně načteno: {encoding}, oddělovač '{sep}'")
                                break
                        except:
                            continue
                    if df is not None:
                        break
                except:
                    continue
        else:
            # Excel
            df = pd.read_excel(file_path)
            print(f"✅ Excel úspěšně načten")
        
        if df is None:
            print("❌ Nepodařilo se načíst soubor!")
            return
        
        print(f"📊 Načteno: {len(df)} řádků, {len(df.columns)} sloupců")
        print(f"🔤 Sloupce: {list(df.columns)}")
        
        # KROK 2: Ukázka dat
        print(f"\n📋 KROK 2: UKÁZKA DAT")
        print("-" * 40)
        print("Prvních 5 řádků:")
        print(df.head().to_string(index=False))
        
        # KROK 3: Kontrola prázdných dat
        print(f"\n🔍 KROK 3: KONTROLA PRÁZDNÝCH DAT")
        print("-" * 40)
        
        empty_rows = df.isnull().all(axis=1).sum()
        print(f"Prázdných řádků: {empty_rows}")
        
        if empty_rows > 0:
            df_clean = df.dropna(how='all')
            print(f"Po odstranění prázdných: {len(df_clean)} řádků")
        else:
            df_clean = df
        
        # KROK 4: Mapování sloupců podle typu
        print(f"\n🔄 KROK 4: MAPOVÁNÍ SLOUPCŮ PRO {file_type}")
        print("-" * 40)
        
        if file_type == "LABARA":
            # LABARA mapování
            nazev_col = None
            mnozstvi_col = None
            
            for col in df_clean.columns:
                col_lower = str(col).lower()
                if any(keyword in col_lower for keyword in ['název', 'nazev', 'popis', 'description']):
                    nazev_col = col
                    print(f"✅ Název nalezen: '{col}'")
                elif any(keyword in col_lower for keyword in ['množství', 'mnozstvi', 'qty', 'ks']):
                    mnozstvi_col = col
                    print(f"✅ Množství nalezeno: '{col}'")
            
            if not nazev_col:
                print("❌ Sloupec názvu nenalezen!")
                print("💡 Dostupné sloupce:")
                for i, col in enumerate(df_clean.columns):
                    print(f"   {i}: '{col}'")
                choice = input("Zadejte číslo sloupce s názvy: ").strip()
                try:
                    nazev_col = df_clean.columns[int(choice)]
                    print(f"✅ Použit sloupec: '{nazev_col}'")
                except:
                    print("❌ Neplatný výběr!")
                    return
            
            if not mnozstvi_col:
                print("⚠️ Sloupec množství nenalezen - použiji defaultní hodnotu 1")
                df_clean['mnozstvi'] = 1
            else:
                df_clean['mnozstvi'] = df_clean[mnozstvi_col]
            
            df_clean['nazev'] = df_clean[nazev_col]
            
        else:  # IMPA
            # IMPA mapování
            material_col = None
            hrubka_col = None
            
            for col in df_clean.columns:
                col_lower = str(col).lower()
                if any(keyword in col_lower for keyword in ['material', 'materiál']):
                    material_col = col
                    print(f"✅ Materiál nalezen: '{col}'")
                elif any(keyword in col_lower for keyword in ['hrubka', 'thickness']):
                    hrubka_col = col
                    print(f"✅ Hrubka nalezena: '{col}'")
            
            if not material_col or not hrubka_col:
                print("❌ Klíčové sloupce nenalezeny!")
                print("💡 Dostupné sloupce:")
                for i, col in enumerate(df_clean.columns):
                    print(f"   {i}: '{col}'")
                
                if not material_col:
                    choice = input("Zadejte číslo sloupce s materiálem: ").strip()
                    try:
                        material_col = df_clean.columns[int(choice)]
                    except:
                        print("❌ Neplatný výběr!")
                        return
                
                if not hrubka_col:
                    choice = input("Zadejte číslo sloupce s hrubkou: ").strip()
                    try:
                        hrubka_col = df_clean.columns[int(choice)]
                    except:
                        print("❌ Neplatný výběr!")
                        return
            
            df_clean['material'] = df_clean[material_col]
            df_clean['hrubka'] = df_clean[hrubka_col]
        
        # KROK 5: Vytváření klíčů
        print(f"\n🔑 KROK 5: VYTVÁŘENÍ KLÍČŮ")
        print("-" * 40)
        
        successful_keys = 0
        failed_keys = 0
        
        for idx, row in df_clean.iterrows():
            if file_type == "LABARA":
                nazev = str(row['nazev'])
                key = create_labara_key(nazev)
                print(f"[{idx+1:2d}] '{nazev[:50]}...' → {key if key else 'NELZE VYTVOŘIT'}")
            else:  # IMPA
                material = str(row['material'])
                hrubka = str(row['hrubka'])
                key = create_impa_key(material, hrubka)
                print(f"[{idx+1:2d}] '{material}' + '{hrubka}' → {key if key else 'NELZE VYTVOŘIT'}")
            
            if key:
                successful_keys += 1
                df_clean.at[idx, 'robust_key'] = key
            else:
                failed_keys += 1
                df_clean.at[idx, 'robust_key'] = None
        
        print(f"\n📊 STATISTIKY KLÍČŮ:")
        print(f"   ✅ Úspěšně vytvořeno: {successful_keys}")
        print(f"   ❌ Neúspěšně: {failed_keys}")
        print(f"   📈 Úspěšnost: {successful_keys/(successful_keys+failed_keys)*100:.1f}%")
        
        # KROK 6: Kontrola katalogu
        print(f"\n📚 KROK 6: KONTROLA KATALOGU")
        print("-" * 40)
        
        try:
            from migrace_standalone_final import get_embedded_catalog
            catalog_df = get_embedded_catalog()
            print(f"✅ Katalog načten: {len(catalog_df)} položek")
            
            # Vytvoříme klíče pro katalog
            catalog_keys = []
            for _, row in catalog_df.iterrows():
                desc = str(row['DESCRIPTION'])
                cat_key = create_catalog_key(desc)
                if cat_key:
                    catalog_keys.append(cat_key)
            
            print(f"📊 Klíčů v katalogu: {len(catalog_keys)}")
            
            # Porovnáme klíče
            order_keys = set(df_clean[df_clean['robust_key'].notna()]['robust_key'])
            catalog_keys_set = set(catalog_keys)
            matching_keys = order_keys.intersection(catalog_keys_set)
            
            print(f"\n🔗 POROVNÁNÍ KLÍČŮ:")
            print(f"   📤 Klíčů z objednávky: {len(order_keys)}")
            print(f"   📚 Klíčů z katalogu: {len(catalog_keys_set)}")
            print(f"   ✅ Shodných klíčů: {len(matching_keys)}")
            
            if matching_keys:
                print(f"\n✅ SHODNÉ KLÍČE (ukázka):")
                for key in list(matching_keys)[:5]:
                    print(f"   → {key}")
            else:
                print(f"\n❌ ŽÁDNÉ SHODNÉ KLÍČE!")
                print(f"💡 Ukázka klíčů z objednávky:")
                for key in list(order_keys)[:5]:
                    print(f"   → {key}")
                print(f"💡 Ukázka klíčů z katalogu:")
                for key in list(catalog_keys_set)[:5]:
                    print(f"   → {key}")
            
            # FINÁLNÍ DIAGNÓZA
            print(f"\n🎯 FINÁLNÍ DIAGNÓZA:")
            print("=" * 40)
            
            if len(matching_keys) == 0:
                print("❌ PROBLÉM: Žádné klíče se neshodují s katalogem!")
                print("💡 MOŽNÉ PŘÍČINY:")
                print("   • Materiály v objednávce nejsou v katalogu")
                print("   • Špatný formát názvů produktů")
                print("   • Chybná tloušťka/hrubka")
                print("   • Problém s vytvářením klíčů")
            elif len(matching_keys) < successful_keys:
                print(f"⚠️ ČÁSTEČNÝ PROBLÉM: Jen {len(matching_keys)} z {successful_keys} klíčů se shoduje")
                print("💡 Některé materiály nejsou v katalogu")
            else:
                print(f"✅ VŠE OK: Všech {len(matching_keys)} klíčů se shoduje!")
                print("💡 Problém může být jinde v kódu")
            
        except Exception as e:
            print(f"❌ Chyba při načítání katalogu: {e}")
        
    except Exception as e:
        print(f"❌ KRITICKÁ CHYBA: {e}")
        import traceback
        traceback.print_exc()

def create_labara_key(nazev):
    """Vytvoří klíč pro LABARA."""
    nazev = str(nazev)
    
    # Hledáme materiál
    material_match = re.search(r'(\d{4}|ENAW\d+\w*)', nazev)
    
    # Hledáme tloušťku
    thickness_match = None
    thickness_match = re.search(r'přířez\s+([\d,]+(?:\.?\d*)?)', nazev, re.IGNORECASE)
    if not thickness_match:
        thickness_match = re.search(r'tloušťka\s+([\d,]+(?:\.?\d*)?)', nazev, re.IGNORECASE)
    if not thickness_match:
        thickness_match = re.search(r'(\d+[,.]?\d*)\s*mm', nazev, re.IGNORECASE)
    if not thickness_match:
        matches = re.findall(r'(\d+[,.]?\d+)(?!\s*x)', nazev, re.IGNORECASE)
        if matches:
            for match in matches:
                num = float(match.replace(',', '.'))
                if 1 <= num <= 500:
                    thickness_match = type('Match', (), {'group': lambda self, x: match})()
                    break
    
    if material_match and thickness_match:
        material = material_match.group(1)
        thickness = float(thickness_match.group(1).replace(',', '.'))
        
        # Normalizace
        if material == "6061":
            material = "ENAW6061T651"
        elif material == "7075":
            material = "ENAW7075"
        elif material == "5754":
            material = "ENAW5754H111"
        
        return f"{material}-{thickness:.2f}"
    return None

def create_impa_key(material, hrubka):
    """Vytvoří klíč pro IMPA."""
    material = str(material).strip()
    hrubka = str(hrubka).strip()
    
    # Normalizace materiálu
    if material in ["6061", "6061T651"]:
        material = "ENAW6061T651"
    elif material in ["7075", "7075T651"]:
        material = "ENAW7075T651"
    elif material == "5754":
        material = "ENAW5754H111"
    
    try:
        hrubka_float = float(str(hrubka).replace(',', '.'))
        return f"{material}-{hrubka_float:.2f}"
    except:
        return None

def create_catalog_key(text):
    """Vytvoří klíč z katalogu."""
    text = str(text)
    material_match = re.search(r'(\d{4}|ENAW\d+\w*|UNIDAL|WELDURAL)', text)
    thickness_match = re.search(r'(\d+[,.]?\d*)\s*$', text)
    
    if material_match and thickness_match:
        material = material_match.group(1)
        thickness_str = thickness_match.group(1).replace(',', '.')
        try:
            thickness_float = float(thickness_str)
            return f"{material}-{thickness_float:.2f}"
        except:
            return None
    return None

if __name__ == "__main__":
    ultimate_debug()
    input("\nStiskněte Enter pro ukončení...")
