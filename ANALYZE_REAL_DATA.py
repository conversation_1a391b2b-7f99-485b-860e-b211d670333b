#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALÝZA REÁLNÝCH VSTUPNÍCH DAT
==============================
Analyzuje všechny soubory ve složce vstup
"""

import pandas as pd
import os
import sys

def analyze_csv_file(file_path):
    """Analyzuje CSV soubor."""
    print(f"\n📄 ANALÝZA CSV: {os.path.basename(file_path)}")
    print("-" * 50)
    
    try:
        # Zkusíme různá kódování
        df = None
        for encoding in ['windows-1250', 'utf-8', 'cp1252', 'iso-8859-2']:
            for sep in [';', ',', '\t']:
                try:
                    test_df = pd.read_csv(file_path, encoding=encoding, sep=sep)
                    if len(test_df.columns) > 1:
                        df = test_df
                        print(f"✅ Načteno s kódováním {encoding}, oddělovač '{sep}'")
                        break
                except:
                    continue
            if df is not None:
                break
        
        if df is None:
            print("❌ Nepodařilo se načíst CSV")
            return
        
        print(f"📊 Řádků: {len(df)}, Sloupců: {len(df.columns)}")
        print(f"🔤 Sloupce: {list(df.columns)}")
        
        print(f"\n📋 UKÁZKA DAT:")
        print(df.head(3).to_string(index=False))
        
        # Analýza sloupců
        print(f"\n🔍 ANALÝZA SLOUPCŮ:")
        for col in df.columns:
            col_lower = str(col).lower()
            sample_values = df[col].dropna().head(3).tolist()
            print(f"   '{col}': {sample_values}")
            
            # Identifikace typu sloupce
            if any(keyword in col_lower for keyword in ['název', 'nazev', 'popis']):
                print(f"      → Pravděpodobně NÁZEV produktu")
            elif any(keyword in col_lower for keyword in ['množství', 'mnozstvi', 'qty', 'ks']):
                print(f"      → Pravděpodobně MNOŽSTVÍ")
            elif any(keyword in col_lower for keyword in ['číslo', 'cislo', 'zboží', 'zbozi']):
                print(f"      → Pravděpodobně ČÍSLO ZBOŽÍ")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")

def analyze_excel_file(file_path):
    """Analyzuje Excel soubor."""
    print(f"\n📊 ANALÝZA EXCEL: {os.path.basename(file_path)}")
    print("-" * 50)
    
    try:
        # Načteme informace o listech
        excel_file = pd.ExcelFile(file_path)
        print(f"📋 Listy: {excel_file.sheet_names}")
        
        # Analyzujeme každý list
        for i, sheet_name in enumerate(excel_file.sheet_names):
            print(f"\n📄 LIST {i+1}: '{sheet_name}'")
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                print(f"   📊 Řádků: {len(df)}, Sloupců: {len(df.columns)}")
                print(f"   🔤 Sloupce: {list(df.columns)}")
                
                if len(df) > 0:
                    print(f"   📋 UKÁZKA DAT:")
                    print("   " + df.head(3).to_string(index=False).replace('\n', '\n   '))
                    
                    # Analýza sloupců
                    print(f"\n   🔍 ANALÝZA SLOUPCŮ:")
                    for col in df.columns:
                        col_lower = str(col).lower()
                        sample_values = df[col].dropna().head(2).tolist()
                        print(f"      '{col}': {sample_values}")
                        
                        # Identifikace typu sloupce
                        if any(keyword in col_lower for keyword in ['material', 'materiál']):
                            print(f"         → Pravděpodobně MATERIÁL (IMPA)")
                        elif any(keyword in col_lower for keyword in ['hrubka', 'thickness']):
                            print(f"         → Pravděpodobně HRUBKA (IMPA)")
                        elif any(keyword in col_lower for keyword in ['sirka', 'width']):
                            print(f"         → Pravděpodobně ŠÍŘKA (IMPA)")
                        elif any(keyword in col_lower for keyword in ['dlzka', 'length']):
                            print(f"         → Pravděpodobně DÉLKA (IMPA)")
                        elif any(keyword in col_lower for keyword in ['pocet', 'qty', 'quantity']):
                            print(f"         → Pravděpodobně MNOŽSTVÍ (IMPA)")
                        elif any(keyword in col_lower for keyword in ['název', 'nazev', 'popis']):
                            print(f"         → Pravděpodobně NÁZEV (DESCON)")
                else:
                    print("   ⚠️ List je prázdný")
                    
            except Exception as e:
                print(f"   ❌ Chyba při načítání listu: {e}")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")

def analyze_all_files():
    """Analyzuje všechny soubory ve složce vstup."""
    print("🔍 ANALÝZA VŠECH REÁLNÝCH VSTUPNÍCH DAT")
    print("=" * 60)
    
    vstup_dir = "vstup"
    if not os.path.exists(vstup_dir):
        print(f"❌ Složka {vstup_dir} neexistuje!")
        return
    
    files = os.listdir(vstup_dir)
    data_files = [f for f in files if f.lower().endswith(('.csv', '.xlsx', '.xls'))]
    
    print(f"📁 Nalezeno {len(data_files)} datových souborů:")
    for f in data_files:
        print(f"   • {f}")
    
    # Analyzujeme každý soubor
    for file_name in data_files:
        file_path = os.path.join(vstup_dir, file_name)
        
        if file_name.lower().endswith('.csv'):
            analyze_csv_file(file_path)
        elif file_name.lower().endswith(('.xlsx', '.xls')):
            analyze_excel_file(file_path)
    
    print(f"\n🎯 SHRNUTÍ ANALÝZY:")
    print("=" * 60)
    print("Na základě analýzy reálných dat:")
    print("")
    print("📄 LABARA (CSV soubory):")
    print("   • Sloupce: pozice, číslo zboží, název, množství, ...")
    print("   • Kódování: windows-1250, oddělovač ';'")
    print("   • Názvy obsahují: 'AL 6061 T651 přířez 20 - 40 - 1290'")
    print("")
    print("📊 IMPA (Excel soubory):")
    print("   • Potřeba analyzovat strukturu jednotlivých IMPA souborů")
    print("")
    print("📋 DESCON (Excel soubory):")
    print("   • Potřeba analyzovat strukturu ALFUN souborů")

if __name__ == "__main__":
    analyze_all_files()
    input("\nStiskněte Enter pro ukončení...")
