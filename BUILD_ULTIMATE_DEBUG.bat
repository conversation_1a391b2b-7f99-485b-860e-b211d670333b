@echo off
title Migrace Objednavek - ULTIMATE DEBUG - Final Build
echo ================================================================
echo   MIGRACE OBJEDNAVEK v7.2 - ULTIMATE DEBUG VERZE
echo   Najde presne, proc nejsou zadna data k ulozeni
echo ================================================================
echo.
echo 🔍 ULTIMATE DEBUG FUNKCE:
echo    ✅ Detailni diagnostika kazde polozky
echo    ✅ Presna identifikace problemu
echo    ✅ Statistiky vytvareni klicu
echo    ✅ Kontrola mapovani s katalogem
echo    ✅ Kritická kontrola prázdných dat
echo    ✅ Návrhy řešení problémů
echo.

echo [1/4] Kontrola Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python neni nainstalovany!
    pause
    exit /b 1
)
echo ✅ Python nalezen

echo [2/4] Kontrola zavislosti...
pip install pandas openpyxl --quiet
echo ✅ Zavislosti pripraveny

echo [3/4] Vytvarim ULTIMATE DEBUG EXE...
echo    🔧 Balim aplikaci s nejpokrocilejsi diagnostikou...
pyinstaller --onefile --windowed ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import tkinter ^
    --hidden-import datetime ^
    --hidden-import traceback ^
    --hidden-import winreg ^
    --name "Migrace_Objednavek_Ultimate_Debug" ^
    --icon=NONE ^
    migrace_standalone_final.py

if errorlevel 1 (
    echo ❌ Build selhal!
    pause
    exit /b 1
)

echo [4/4] Finalizace...
if exist "dist\Migrace_Objednavek_Ultimate_Debug.exe" (
    copy "dist\Migrace_Objednavek_Ultimate_Debug.exe" ".\Migrace_Objednavek_Ultimate_Debug.exe" >nul
    echo.
    echo ================================================================
    echo   🎉 SUCCESS! ULTIMATE DEBUG EXE VYTVOŘEN!
    echo ================================================================
    echo.
    echo 📁 Soubor: Migrace_Objednavek_Ultimate_Debug.exe
    for %%A in ("Migrace_Objednavek_Ultimate_Debug.exe") do echo 📊 Velikost: %%~zA bytes
    echo.
    echo 🔍 ULTIMATE DEBUG FUNKCE:
    echo    • Přesně ukáže, proč nejsou žádná data
    echo    • Detailní diagnostika každé položky
    echo    • Statistiky vytváření klíčů
    echo    • Kontrola mapování s katalogem
    echo    • Kritická kontrola prázdných dat
    echo    • Návrhy konkrétních řešení
    echo.
    echo 💡 CO NYNÍ UVIDÍTE:
    echo    ❌ KRITICKÝ PROBLÉM: ŽÁDNÁ PLATNÁ DATA!
    echo    🔍 DIAGNOSTIKA:
    echo       • Vstupních řádků: X
    echo       • Vytvořených klíčů: Y
    echo       • Namapovaných s katalogem: Z
    echo       💡 PROBLÉM: [přesný důvod]
    echo       💡 ŘEŠENÍ: [konkrétní kroky]
    echo.
    echo ✅ NYNÍ NAJDETE PŘESNĚ, CO JE ŠPATNĚ!
    echo.
) else (
    echo ❌ EXE soubor nebyl vytvoren!
    pause
    exit /b 1
)

echo Chcete spustit ULTIMATE DEBUG verzi? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo Spoustim ULTIMATE DEBUG aplikaci...
    start Migrace_Objednavek_Ultimate_Debug.exe
)

pause
