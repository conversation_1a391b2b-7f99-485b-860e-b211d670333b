#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NAJDI PROBLÉM - JEDNODUCHÝ DIAGNOSTICKÝ NÁSTROJ
===============================================
Pomůže najít přesný problém s tvými daty
"""

import pandas as pd
import os
import sys
import re

def find_problem():
    """Najde problém s daty."""
    print("🔍 NAJDI PROBLÉM - DIAGNOSTICKÝ NÁSTROJ")
    print("=" * 50)
    
    # Najdeme soubory v aktuálním adresáři
    print("📁 HLEDÁNÍ SOUBORŮ V AKTUÁLNÍM ADRESÁŘI:")
    current_dir = os.getcwd()
    files = []
    
    for file in os.listdir(current_dir):
        if file.lower().endswith(('.xlsx', '.xls', '.csv')):
            files.append(file)
            print(f"   {len(files)}: {file}")
    
    if not files:
        print("❌ Žádné Excel/CSV soubory nenalezeny!")
        print("💡 Zkopírujte svůj soubor do tohoto adresáře:")
        print(f"   {current_dir}")
        return
    
    # Výběr souboru
    print(f"\n📋 Vyberte soubor k analýze:")
    choice = input("Číslo souboru: ").strip()
    
    try:
        file_path = files[int(choice) - 1]
        print(f"✅ Vybrán soubor: {file_path}")
    except:
        print("❌ Neplatný výběr!")
        return
    
    # Analýza souboru
    analyze_file(file_path)

def analyze_file(file_path):
    """Analyzuje konkrétní soubor."""
    print(f"\n🔍 ANALÝZA SOUBORU: {file_path}")
    print("=" * 50)
    
    try:
        # Načtení souboru
        if file_path.lower().endswith('.csv'):
            # CSV - zkusíme různá kódování
            df = None
            for encoding in ['windows-1250', 'utf-8', 'cp1252']:
                for sep in [';', ',', '\t']:
                    try:
                        test_df = pd.read_csv(file_path, encoding=encoding, sep=sep)
                        if len(test_df.columns) > 1:
                            df = test_df
                            print(f"✅ CSV načten: {encoding}, oddělovač '{sep}'")
                            break
                    except:
                        continue
                if df is not None:
                    break
        else:
            # Excel
            df = pd.read_excel(file_path)
            print(f"✅ Excel načten")
        
        if df is None:
            print("❌ Nepodařilo se načíst soubor!")
            return
        
        print(f"📊 Načteno: {len(df)} řádků, {len(df.columns)} sloupců")
        
        # Ukázka dat
        print(f"\n📋 UKÁZKA DAT:")
        print(f"Sloupce: {list(df.columns)}")
        print("\nPrvních 3 řádků:")
        print(df.head(3).to_string(index=False))
        
        # Kontrola prázdných dat
        empty_rows = df.isnull().all(axis=1).sum()
        print(f"\n📊 STATISTIKY:")
        print(f"   Prázdných řádků: {empty_rows}")
        print(f"   Platných řádků: {len(df) - empty_rows}")
        
        # Určení typu a analýza klíčů
        file_type = determine_file_type(df, file_path)
        print(f"   Detekovaný typ: {file_type}")
        
        if file_type == "LABARA":
            analyze_labara(df)
        elif file_type == "IMPA":
            analyze_impa(df)
        else:
            print("❓ Neznámý typ souboru")
        
    except Exception as e:
        print(f"❌ CHYBA: {e}")
        import traceback
        traceback.print_exc()

def determine_file_type(df, file_path):
    """Určí typ souboru."""
    columns = [str(col).lower() for col in df.columns]
    
    # IMPA indikátory
    if any('material' in col for col in columns) and any('hrubka' in col for col in columns):
        return "IMPA"
    
    # LABARA indikátory
    if any('název' in col or 'nazev' in col for col in columns):
        return "LABARA"
    
    # Podle názvu souboru
    if 'impa' in file_path.lower():
        return "IMPA"
    elif 'labara' in file_path.lower() or file_path.lower().endswith('.csv'):
        return "LABARA"
    
    return "NEZNÁMÝ"

def analyze_labara(df):
    """Analyzuje LABARA data."""
    print(f"\n🔍 ANALÝZA LABARA DAT:")
    print("-" * 30)
    
    # Najdeme sloupec s názvy
    nazev_col = None
    for col in df.columns:
        col_lower = str(col).lower()
        if any(keyword in col_lower for keyword in ['název', 'nazev', 'popis']):
            nazev_col = col
            break
    
    if not nazev_col:
        print("❌ Sloupec s názvy nenalezen!")
        return
    
    print(f"✅ Sloupec názvů: '{nazev_col}'")
    
    # Testujeme vytváření klíčů
    successful = 0
    failed = 0
    
    print(f"\n🔑 TESTOVÁNÍ VYTVÁŘENÍ KLÍČŮ:")
    for idx, row in df.head(10).iterrows():
        nazev = str(row[nazev_col])
        key = create_labara_key(nazev)
        
        if key:
            successful += 1
            print(f"✅ [{idx+1:2d}] '{nazev[:40]}...' → {key}")
        else:
            failed += 1
            print(f"❌ [{idx+1:2d}] '{nazev[:40]}...' → NELZE VYTVOŘIT")
    
    print(f"\n📊 VÝSLEDKY LABARA:")
    print(f"   ✅ Úspěšných klíčů: {successful}")
    print(f"   ❌ Neúspěšných: {failed}")
    
    if failed > 0:
        print(f"\n💡 PROBLÉMY S LABARA:")
        print("   • Názvy produktů nemají správný formát")
        print("   • Chybí materiál nebo tloušťka v názvu")
        print("   • Příklad správného formátu: '5083 litá přířez 10,00 - 80 x 90'")

def analyze_impa(df):
    """Analyzuje IMPA data."""
    print(f"\n🔍 ANALÝZA IMPA DAT:")
    print("-" * 30)
    
    # Najdeme klíčové sloupce
    material_col = None
    hrubka_col = None
    
    for col in df.columns:
        col_lower = str(col).lower()
        if 'material' in col_lower:
            material_col = col
        elif 'hrubka' in col_lower or 'thickness' in col_lower:
            hrubka_col = col
    
    if not material_col:
        print("❌ Sloupec materiálu nenalezen!")
        return
    if not hrubka_col:
        print("❌ Sloupec hrubky nenalezen!")
        return
    
    print(f"✅ Sloupec materiálu: '{material_col}'")
    print(f"✅ Sloupec hrubky: '{hrubka_col}'")
    
    # Testujeme vytváření klíčů
    successful = 0
    failed = 0
    
    print(f"\n🔑 TESTOVÁNÍ VYTVÁŘENÍ KLÍČŮ:")
    for idx, row in df.head(10).iterrows():
        material = str(row[material_col])
        hrubka = str(row[hrubka_col])
        key = create_impa_key(material, hrubka)
        
        if key:
            successful += 1
            print(f"✅ [{idx+1:2d}] '{material}' + '{hrubka}' → {key}")
        else:
            failed += 1
            print(f"❌ [{idx+1:2d}] '{material}' + '{hrubka}' → NELZE VYTVOŘIT")
    
    print(f"\n📊 VÝSLEDKY IMPA:")
    print(f"   ✅ Úspěšných klíčů: {successful}")
    print(f"   ❌ Neúspěšných: {failed}")
    
    if failed > 0:
        print(f"\n💡 PROBLÉMY S IMPA:")
        print("   • Materiál nebo hrubka mají neplatný formát")
        print("   • Příklad správného formátu: Material='5083', Hrubka='10.0'")

def create_labara_key(nazev):
    """Vytvoří klíč pro LABARA."""
    nazev = str(nazev)
    
    # Hledáme materiál
    material_match = re.search(r'(\d{4}|ENAW\d+\w*)', nazev)
    
    # Hledáme tloušťku
    thickness_match = None
    thickness_match = re.search(r'přířez\s+([\d,]+(?:\.?\d*)?)', nazev, re.IGNORECASE)
    if not thickness_match:
        thickness_match = re.search(r'tloušťka\s+([\d,]+(?:\.?\d*)?)', nazev, re.IGNORECASE)
    if not thickness_match:
        thickness_match = re.search(r'(\d+[,.]?\d*)\s*mm', nazev, re.IGNORECASE)
    if not thickness_match:
        matches = re.findall(r'(\d+[,.]?\d+)(?!\s*x)', nazev, re.IGNORECASE)
        if matches:
            for match in matches:
                num = float(match.replace(',', '.'))
                if 1 <= num <= 500:
                    thickness_match = type('Match', (), {'group': lambda self, x: match})()
                    break
    
    if material_match and thickness_match:
        material = material_match.group(1)
        thickness = float(thickness_match.group(1).replace(',', '.'))
        
        # Normalizace
        if material == "6061":
            material = "ENAW6061T651"
        elif material == "7075":
            material = "ENAW7075"
        elif material == "5754":
            material = "ENAW5754H111"
        
        return f"{material}-{thickness:.2f}"
    return None

def create_impa_key(material, hrubka):
    """Vytvoří klíč pro IMPA."""
    material = str(material).strip()
    hrubka = str(hrubka).strip()
    
    # Normalizace materiálu
    if material in ["6061", "6061T651"]:
        material = "ENAW6061T651"
    elif material in ["7075", "7075T651"]:
        material = "ENAW7075T651"
    elif material == "5754":
        material = "ENAW5754H111"
    
    try:
        hrubka_float = float(str(hrubka).replace(',', '.'))
        return f"{material}-{hrubka_float:.2f}"
    except:
        return None

if __name__ == "__main__":
    find_problem()
    input("\nStiskněte Enter pro ukončení...")
