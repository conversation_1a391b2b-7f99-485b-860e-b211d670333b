#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST POUZE DESCON
================
Testuje pouze DESCON funkci
"""

import pandas as pd
import os
import sys
import time

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_final_all_fixed import process_descon_order_fixed
    print("✅ Modul úspěšně načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def test_descon_only():
    """Test pouze DESCON."""
    print("🧪 TEST POUZE DESCON")
    print("=" * 40)
    
    # Vytvoříme testovací DESCON data
    descon_data = {
        'nazev': [
            '5083 litá přířez 12,00 - 100 x 200',
            'ENAW6061T651 válcovaná 8,00 - 150 x 300',
            'ENAW5754H111 tloušťka 15,00 mm',
            'Prázdný řádek 1',  # Tyto budou odstraněny (poslední 3)
            'Prázdn<PERSON> řádek 2',
            'Prázdný řádek 3'
        ],
        'mnozstvi': [1, 2, 1, 0, 0, 0]
    }
    
    df = pd.DataFrame(descon_data)
    test_file = 'test_descon_only.xlsx'
    
    # Uložíme s malou pauzou
    df.to_excel(test_file, index=False)
    time.sleep(0.5)  # Pauza pro uvolnění souboru
    print(f"✅ Vytvořen testovací DESCON soubor: {test_file}")
    
    try:
        print(f"\n🚀 SPOUŠTÍM DESCON TEST:")
        print("-" * 30)
        
        result_df = process_descon_order_fixed(test_file)
        
        print(f"\n📊 VÝSLEDEK DESCON:")
        print(f"   Zpracováno: {len(result_df)} položek")
        
        if len(result_df) > 0:
            print("✅ DESCON FUNGUJE!")
            print(f"\n📋 UKÁZKA VÝSTUPU:")
            print(result_df.to_string(index=False))
        else:
            print("❌ DESCON NEFUNGUJE - žádné platné položky!")
            
    except Exception as e:
        print(f"❌ CHYBA DESCON: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Úklid s pauzou
        time.sleep(1)
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
                print(f"\n🧹 Smazán testovací soubor: {test_file}")
        except:
            print(f"⚠️ Nepodařilo se smazat testovací soubor: {test_file}")

if __name__ == "__main__":
    test_descon_only()
    input("\nStiskněte Enter pro ukončení...")
