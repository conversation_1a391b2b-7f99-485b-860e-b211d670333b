@echo off
setlocal enabledelayedexpansion
title Migrace Objednavek v7.1 - Universal Windows Edition
chcp 1250 >nul

echo ========================================================
echo              MIGRACE OBJEDNAVEK v7.1
echo        Universal Windows Edition - Final Solution       
echo ========================================================
echo.

REM Pokus 1: Spust uz existujici EXE
echo [METODA 1] Hledam jiz existujici Windows EXE...
if exist "Migrace_Objednavek.exe" (
    echo [OK] Nalezen Windows EXE - spoustim...
    echo.
    start "" "Migrace_Objednavek.exe"
    goto :success
)

REM Pokus 2: Zkontroluj portable Python
echo [METODA 2] Kontroluji portable Python...
if exist "python-portable\python.exe" (
    echo [OK] Portable Python nalezen - spoustim aplikaci...
    echo.
    "python-portable\python.exe" main_fixed.py
    goto :success
)

REM Pokus 3: Zkontroluj system Python
echo [METODA 3] Kontroluji systemovy Python...
python --version >nul 2>&1
if not errorlevel 1 (
    echo [OK] System Python nalezen - spoustim aplikaci...
    echo.
    python main_fixed.py
    goto :success
)

REM Pokus 4: Auto-setup
echo [METODA 4] Zadny Python nalezen - spoustim auto-setup...
echo.
echo Tento script automaticky nastavi aplikaci.
echo Vyberte preferovanou metodu:
echo.
echo [1] Portable Python (doporuceno)
echo [2] Build nativni Windows EXE
echo [3] Rucni instalace system Python
echo.

set /p method="Vyberte metodu (1-3): "

if "%method%"=="1" goto :portable_setup
if "%method%"=="2" goto :exe_build
if "%method%"=="3" goto :python_install

echo [!] Neplatna volba - pokracuji s portable Python...

:portable_setup
echo.
echo [PORTABLE SETUP] Stahuji a nastavuji portable Python...
echo.
call :download_portable_python
if errorlevel 1 goto :error
echo [OK] Portable Python pripraven!
echo Spoustim aplikaci...
"python-portable\python.exe" main_fixed.py
goto :success

:exe_build
echo.
echo [EXE BUILD] Vytvarim nativni Windows EXE...
echo.
call :build_native_exe
if errorlevel 1 goto :portable_setup
echo [OK] Windows EXE vytvoren!
echo Spoustim EXE...
start "" "Migrace_Objednavek.exe"
goto :success

:python_install
echo.
echo [PYTHON INSTALL] Presmerovavam na python.org...
start https://python.org/downloads/
echo.
echo Po instalaci Pythonu spustte tento script znovu.
goto :end

:download_portable_python
echo [1/5] Vytvarim portable slozku...
if not exist "python-portable" mkdir "python-portable"

echo [2/5] Stahuji Python Embeddable (25 MB)...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; try { Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.0/python-3.11.0-embed-amd64.zip' -OutFile 'python-portable\python-embed.zip' } catch { exit 1 }}"
if not exist "python-portable\python-embed.zip" exit /b 1

echo [3/5] Rozbaluji Python...
powershell -Command "try { Expand-Archive -Path 'python-portable\python-embed.zip' -DestinationPath 'python-portable\' -Force } catch { exit 1 }"
del "python-portable\python-embed.zip" >nul 2>&1

echo [4/5] Instaluji pip a knihovny...
cd python-portable
echo import sys > get-pip.py
echo import urllib.request >> get-pip.py
echo urllib.request.urlretrieve('https://bootstrap.pypa.io/get-pip.py', 'get-pip-real.py') >> get-pip.py
echo exec(open('get-pip-real.py').read()) >> get-pip.py
python.exe get-pip.py --no-warn-script-location

echo [5/5] Instaluji pandas a openpyxl...
python.exe -m pip install pandas openpyxl --no-warn-script-location
cd ..
exit /b 0

:build_native_exe
python --version >nul 2>&1
if errorlevel 1 (
    echo [!] Python neni dostupny pro build
    exit /b 1
)

echo [1/3] Instaluji PyInstaller...
pip install pyinstaller --quiet

echo [2/3] Vytvarim Windows EXE (2-5 minut)...
pyinstaller --onefile --windowed ^
    --add-data "ALDE_katalog_polozek.xlsx;." ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import tkinter ^
    --name "Migrace_Objednavek" ^
    main_fixed.py

echo [3/3] Kopiruji EXE...
if exist "dist\Migrace_Objednavek.exe" (
    copy "dist\Migrace_Objednavek.exe" ".\Migrace_Objednavek.exe" >nul
    exit /b 0
) else (
    exit /b 1
)

:success
echo.
echo ========================================================
echo                  USPESNE SPUSTENO!
echo ========================================================
echo.
echo [i] Aplikace bezi na pozadi
echo [i] Pro testovani pouzijte soubory z IMPA_Test_Data\
echo [i] Vystup se ulozi do Documents\Migrace_Vystupy\
echo [i] Pro pristi spusteni staci dvojklik na tento soubor
echo.
goto :end

:error
echo.
echo ========================================================
echo                        CHYBA!
echo ========================================================
echo.
echo Vsechny automaticke metody selhaly.
echo.
echo MANUALNI RESENI:
echo 1. Nainstalujte Python z https://python.org
echo 2. Pri instalaci zasktrnete "Add Python to PATH"
echo 3. Restartujte pocitac
echo 4. Spustte tento script znovu
echo.

:end
pause
