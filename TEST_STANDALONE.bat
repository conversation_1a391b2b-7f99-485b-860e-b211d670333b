@echo off
title Test Migrace O<PERSON> - Standalone
echo ================================================
echo   TEST MIGRACE OBJEDNAVEK - STANDALONE VERZE
echo ================================================
echo.
echo 🧪 Spoustim test standalone aplikace...
echo.

echo [1/2] Kontrola Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python neni nainstalovany!
    pause
    exit /b 1
)
echo ✅ Python nalezen

echo [2/2] Instalace zavislosti (pokud potreba)...
pip install pandas openpyxl --quiet
echo ✅ Zavislosti pripraveny

echo.
echo 🚀 Spoustim standalone aplikaci...
echo    📊 Katalog: 508 polozek (vestaveny)
echo    📁 Testovaci data: IMPA_Test_Data/
echo.

python migrace_standalone_final.py

echo.
echo Test dokoncen.
pause
