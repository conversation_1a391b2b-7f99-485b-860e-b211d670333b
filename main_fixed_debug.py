import sys
import os
import traceback
import pandas as pd
import re
import tkinter as tk
from tkinter import Toplevel, Entry, Label, Button, messagebox
from tkinter import filedialog

# ==============================================================================
# ČÁST 0: Debug a Error Handling
# ==============================================================================

def setup_debug_logging():
    """Nastavení debug logování"""
    try:
        # Vytvoř debug složku
        debug_dir = os.path.join(os.path.expanduser("~"), "Documents", "Migrace_Debug")
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)
        
        # Debug log soubor
        debug_file = os.path.join(debug_dir, "migrace_debug.txt")
        
        def debug_print(msg):
            try:
                with open(debug_file, "a", encoding="utf-8") as f:
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"[{timestamp}] {msg}\n")
                print(msg)  # Také vypíše do konzole
            except:
                print(msg)  # Fallback jen do konzole
        
        return debug_print
    except:
        # Fallback - jen print
        return print

# Globální debug funkce
debug = setup_debug_logging()

def safe_main():
    """Bezpečné spuštění s kompletním error handlingem"""
    try:
        debug("=== SPUŠTĚNÍ MIGRACE OBJEDNÁVEK ===")
        debug(f"Python verze: {sys.version}")
        debug(f"Pracovní adresář: {os.getcwd()}")
        debug(f"Argumenty: {sys.argv}")
        
        # Zkontroluj dostupnost knihoven
        debug("Kontroluji knihovny...")
        try:
            import pandas as pd
            debug(f"✓ Pandas verze: {pd.__version__}")
        except ImportError as e:
            debug(f"✗ Pandas chyba: {e}")
            show_library_error("pandas", e)
            return
            
        try:
            import openpyxl
            debug(f"✓ OpenPyXL verze: {openpyxl.__version__}")
        except ImportError as e:
            debug(f"✗ OpenPyXL chyba: {e}")
            show_library_error("openpyxl", e)
            return
            
        try:
            import tkinter as tk
            debug("✓ Tkinter dostupný")
        except ImportError as e:
            debug(f"✗ Tkinter chyba: {e}")
            show_library_error("tkinter", e)
            return
        
        # Zkontroluj katalog
        debug("Kontroluji katalogový soubor...")
        catalog_path = find_catalog_file()
        if not catalog_path:
            debug("✗ Katalog nenalezen!")
            show_catalog_error()
            return
        debug(f"✓ Katalog nalezen: {catalog_path}")
        
        # Spusť hlavní aplikaci
        debug("Spouštím hlavní aplikaci...")
        start_main_application()
        debug("=== APLIKACE UKONČENA ===")
        
    except Exception as e:
        debug(f"KRITICKÁ CHYBA: {e}")
        debug(f"Traceback: {traceback.format_exc()}")
        show_critical_error(e)

def find_catalog_file():
    """Najde katalogový soubor"""
    possible_names = [
        "ALDE_katalog_polozek.xlsx",
        "ALDE_katalog_položek.xlsx", 
        "ALDE_katalog_polozek.xlsx",
        "alde_katalog_polozek.xlsx"
    ]
    
    # Zkontroluj aktuální složku
    for name in possible_names:
        if os.path.exists(name):
            return name
    
    # Zkontroluj složku aplikace
    app_dir = os.path.dirname(os.path.abspath(__file__))
    for name in possible_names:
        path = os.path.join(app_dir, name)
        if os.path.exists(path):
            return path
    
    return None

def show_library_error(library_name, error):
    """Zobrazí chybu chybějící knihovny"""
    try:
        root = tk.Tk()
        root.withdraw()  # Skryj hlavní okno
        
        msg = f"""CHYBA: Chybí knihovna {library_name}

Detail chyby: {error}

ŘEŠENÍ:
1. Otevřete Command Prompt (cmd)
2. Spusťte: pip install {library_name}
3. Nebo: pip install -r requirements.txt
4. Restartujte aplikaci

Pro více informací zkontrolujte soubor:
Documents/Migrace_Debug/migrace_debug.txt"""
        
        messagebox.showerror("Chybí knihovna", msg)
        root.destroy()
    except:
        print(f"CHYBA: Chybí knihovna {library_name}: {error}")
        input("Stiskněte Enter pro ukončení...")

def show_catalog_error():
    """Zobrazí chybu chybějícího katalogu"""
    try:
        root = tk.Tk()
        root.withdraw()
        
        msg = """CHYBA: Nenalezen katalogový soubor!

Očekávané názvy:
• ALDE_katalog_polozek.xlsx
• ALDE_katalog_položek.xlsx

ŘEŠENÍ:
1. Zkontrolujte, že katalog je ve stejné složce jako aplikace
2. Zkontrolujte název souboru (česká diakritika)
3. Zkontrolujte, že soubor není poškozen

Aktuální složka:
""" + os.getcwd()
        
        messagebox.showerror("Chybí katalog", msg)
        root.destroy()
    except:
        print("CHYBA: Nenalezen katalogový soubor!")
        print(f"Aktuální složka: {os.getcwd()}")
        input("Stiskněte Enter pro ukončení...")

def show_critical_error(error):
    """Zobrazí kritickou chybu"""
    try:
        root = tk.Tk()
        root.withdraw()
        
        msg = f"""KRITICKÁ CHYBA V APLIKACI

Chyba: {error}

Debug informace byly uloženy do:
Documents/Migrace_Debug/migrace_debug.txt

Kontaktujte podporu s těmito informacemi."""
        
        messagebox.showerror("Kritická chyba", msg)
        root.destroy()
    except:
        print(f"KRITICKÁ CHYBA: {error}")
        input("Stiskněte Enter pro ukončení...")

# ==============================================================================
# ČÁST 1: Pomocné funkce (zachovány z originalu)
# ==============================================================================

def get_documents_folder():
    """Cross-platform detekce Documents složky"""
    try:
        import os
        home = os.path.expanduser("~")
        
        # Windows
        documents_paths = [
            os.path.join(home, "Documents"),
            os.path.join(home, "Dokumenty"), 
            os.path.join(home, "My Documents"),
            home  # Fallback
        ]
        
        for path in documents_paths:
            if os.path.exists(path):
                debug(f"Documents složka nalezena: {path}")
                return path
        
        debug(f"Documents fallback: {home}")
        return home
    except Exception as e:
        debug(f"Chyba při hledání Documents: {e}")
        return os.path.expanduser("~")

# ==============================================================================
# ČÁST 2: Dialogové okno pro zadání chybějících dat  
# ==============================================================================

class InputDialog(Toplevel):
    def __init__(self, parent, title=None):
        try:
            super().__init__(parent)
            self.parent = parent
            self.transient(parent)
            if title: 
                self.title(title)
            self.result = None
            
            # Nastavení okna
            self.geometry("400x150")
            self.resizable(False, False)
            
            self.body = tk.Frame(self)
            self.initial_focus = self.body_contents(self.body)
            self.body.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
            self.buttonbox()
            
            self.grab_set()
            
            if not self.initial_focus: 
                self.initial_focus = self
            self.protocol("WM_DELETE_WINDOW", self.cancel)
            
            # Vycentruj okno
            self.center_window()
            
            self.initial_focus.focus_set()
            debug("InputDialog vytvořen úspěšně")
            
            self.wait_window(self)
            
        except Exception as e:
            debug(f"Chyba v InputDialog: {e}")
            raise

    def center_window(self):
        """Vycentruje okno na obrazovce"""
        try:
            self.update_idletasks()
            x = (self.winfo_screenwidth() // 2) - (self.winfo_width() // 2)
            y = (self.winfo_screenheight() // 2) - (self.winfo_height() // 2)
            self.geometry(f"+{x}+{y}")
        except:
            pass

    def body_contents(self, master):
        try:
            Label(master, text="Datum dodání (DD.MM.RRRR):", font=("Arial", 10)).grid(row=0, column=0, sticky="w", pady=2)
            Label(master, text="Číslo zákazníka:", font=("Arial", 10)).grid(row=1, column=0, sticky="w", pady=2)
            
            self.entry_date = Entry(master, font=("Arial", 10), width=25)
            self.entry_customer = Entry(master, font=("Arial", 10), width=25)
            
            # Výchozí hodnoty
            self.entry_date.insert(0, "06.06.2025")
            self.entry_customer.insert(0, "25555308")
            
            self.entry_date.grid(row=0, column=1, padx=5, pady=2)
            self.entry_customer.grid(row=1, column=1, padx=5, pady=2)
            
            return self.entry_date
        except Exception as e:
            debug(f"Chyba v body_contents: {e}")
            raise

    def buttonbox(self):
        try:
            box = tk.Frame(self)
            Button(box, text="OK", width=10, command=self.ok, default=tk.ACTIVE, font=("Arial", 10)).pack(side=tk.LEFT, padx=5, pady=5)
            Button(box, text="Zrušit", width=10, command=self.cancel, font=("Arial", 10)).pack(side=tk.LEFT, padx=5, pady=5)
            
            self.bind("<Return>", self.ok)
            self.bind("<Escape>", self.cancel)
            box.pack(pady=5)
        except Exception as e:
            debug(f"Chyba v buttonbox: {e}")
            raise

    def ok(self, event=None):
        try:
            dated = self.entry_date.get().strip()
            customer_no = self.entry_customer.get().strip()
            
            if not dated or not customer_no:
                messagebox.showwarning("Neúplné údaje", "Prosím vyplňte všechna pole.")
                return
            
            self.result = (dated, customer_no)
            debug(f"InputDialog OK: datum={dated}, zákazník={customer_no}")
            self.destroy()
        except Exception as e:
            debug(f"Chyba v InputDialog.ok: {e}")
            messagebox.showerror("Chyba", f"Chyba při zpracování údajů: {e}")

    def cancel(self, event=None):
        try:
            debug("InputDialog zrušen")
            self.destroy()
        except Exception as e:
            debug(f"Chyba v InputDialog.cancel: {e}")

# ==============================================================================
# ČÁST 3: Zpracování objednávek (zjednodušená verze pro debug)
# ==============================================================================

def process_labara_order(order_path, catalog_path, dated_input, customer_no_input):
    """Zpracuje LABARA objednávku"""
    try:
        debug(f"Zpracovávám LABARA: {order_path}")
        debug(f"Katalog: {catalog_path}")
        debug(f"Datum: {dated_input}, Zákazník: {customer_no_input}")
        
        # Načti data s error handlingem
        try:
            order_items_df = pd.read_csv(order_path, encoding='windows-1250', sep=';')
            debug(f"✓ Objednávka načtena: {len(order_items_df)} řádků")
        except Exception as e:
            debug(f"✗ Chyba načítání objednávky: {e}")
            raise ValueError(f"Chyba při načítání CSV souboru.\n\nMožné příčiny:\n- Špatné kódování (očekáváno windows-1250)\n- Nesprávný oddělovač (očekáván ;)\n- Poškozený soubor\n\nDetail: {e}")
        
        try:
            alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
            debug(f"✓ Katalog načten: {len(alde_catalog_df)} položek")
        except Exception as e:
            debug(f"✗ Chyba načítání katalogu: {e}")
            raise ValueError(f"Chyba při načítání katalogu.\n\nDetail: {e}")
        
        # Základní validace dat
        required_columns = ['nazev', 'mnozstvi']
        missing_columns = [col for col in required_columns if col not in order_items_df.columns]
        if missing_columns:
            debug(f"✗ Chybí sloupce: {missing_columns}")
            available_cols = list(order_items_df.columns)
            raise ValueError(f"Chybí povinné sloupce: {missing_columns}\n\nDostupné sloupce: {available_cols}")
        
        # Pokračuj se zpracováním...
        debug("Pokračuji se zpracováním dat...")
        
        # Pattern matching funkce (zjednodušené)
        def get_robust_key(text):
            if pd.isna(text):
                return None
            text = str(text)
            material_num_match = re.search(r'(\d{4})', text)
            thickness_match = re.search(r'přířez\s+([\d,]+)', text, re.IGNORECASE)
            if not thickness_match:
                thickness_match = re.search(r'přířez\s+([\d,]+)\s*mm', text, re.IGNORECASE)
            
            if material_num_match and thickness_match:
                material_num = material_num_match.group(1)
                thickness = float(thickness_match.group(1).replace(',', '.'))
                return f"{material_num}-{thickness:.2f}"
            return None
        
        def get_catalog_key(text):
            if pd.isna(text):
                return None
            text = str(text)
            material_num_match = re.search(r'(\d{4})', text)
            thickness_match = re.search(r'([\d,]+\.\d{2})', text.replace(',', '.'))
            if material_num_match and thickness_match:
                material_num = material_num_match.group(1)
                thickness = thickness_match.group(1)
                return f"{material_num}-{thickness}"
            return None

        # Aplikuj matching
        order_items_df['robust_key'] = order_items_df['nazev'].apply(get_robust_key)
        alde_catalog_df['robust_key'] = alde_catalog_df['DESCRIPTION'].apply(get_catalog_key)
        alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO'}, inplace=True)
        
        debug(f"Pattern matching dokončen")
        
        # Propojení dat
        final_df = pd.merge(order_items_df, alde_catalog_df, on='robust_key', how='left')
        final_df.drop_duplicates(subset=['nazev'], keep='first', inplace=True)
        
        # Kontrola nespárovaných položek
        unmatched = final_df[final_df['CATALOG_NO'].isnull()]
        if not unmatched.empty:
            debug(f"⚠ Nespárované položky: {len(unmatched)}")
            unmatched_list = unmatched['nazev'].head(5).tolist()
            messagebox.showwarning("Upozornění", f"Pro {len(unmatched)} položek nebyla nalezena shoda.\n\nPříklady:\n" + "\n".join(unmatched_list[:3]))
        
        # Příprava výstupu
        customer_po_no = os.path.splitext(os.path.basename(order_path))[0]
        
        # Zpracování množství
        final_df['mnozstvi'] = final_df['mnozstvi'].astype(str).str.replace(' ks', '', regex=False)
        final_df['mnozstvi'] = pd.to_numeric(final_df['mnozstvi'], errors='coerce').fillna(1).astype(int)
        
        # Parsování rozměrů
        def parse_dimensions(nazev):
            if pd.isna(nazev):
                return None, None
            nazev = str(nazev)
            match_dims = re.search(r'-\s*([\d,\.]+)\s*[-x]\s*([\d,\.]+)', nazev, re.IGNORECASE)
            if match_dims:
                return match_dims.group(1).strip().replace(',', '.'), match_dims.group(2).strip().replace(',', '.')
            return None, None

        dims_data = final_df['nazev'].apply(lambda x: pd.Series(parse_dimensions(x)))
        final_df[['W', 'L']] = dims_data
        
        # Vytvoření výstupního DataFrame
        output_df = pd.DataFrame({
            'CONTRACT': 'CZ21',
            'CUSTOMER_NO': customer_no_input,
            'CATALOG_NO': final_df['CATALOG_NO'],
            'QTY': final_df['mnozstvi'],
            'DATED': pd.to_datetime(dated_input, format='%d.%m.%Y').strftime('%d.%m.%Y'),
            'PRICE': 0,
            'CURRENCY_CODE': 'CZK',
            'CUSTOMER_PO_NO': customer_po_no,
            'W': final_df['W'],
            'L': final_df['L']
        })
        
        # Odstranění řádků bez CATALOG_NO
        output_df.dropna(subset=['CATALOG_NO'], inplace=True)
        debug(f"Výstupní data: {len(output_df)} řádků")
        
        # Uložení do Documents
        documents_folder = get_documents_folder()
        output_folder = os.path.join(documents_folder, 'Migrace_Vystupy')
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            
        output_filename = f"vystup_{customer_po_no}.csv"
        output_path = os.path.join(output_folder, output_filename)
        
        output_df.to_csv(output_path, index=False, sep=';', encoding='utf-8-sig')
        debug(f"✓ Výstup uložen: {output_path}")
        
        return output_path
        
    except Exception as e:
        debug(f"✗ Chyba v process_labara_order: {e}")
        debug(f"Traceback: {traceback.format_exc()}")
        raise

def process_descon_order(order_path, catalog_path):
    """Zpracuje DESCON objednávku (zjednodušená verze)"""
    try:
        debug(f"Zpracovávám DESCON: {order_path}")
        
        # Načti Excel data
        order_items_df = pd.read_excel(order_path, sheet_name=1, header=0, skipfooter=3)
        alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
        
        debug(f"DESCON data načtena: {len(order_items_df)} řádků")
        
        # Jednoduchý výstup pro test
        documents_folder = get_documents_folder()
        output_folder = os.path.join(documents_folder, 'Migrace_Vystupy')
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            
        output_filename = "vystup_descon_test.csv"
        output_path = os.path.join(output_folder, output_filename)
        
        # Testovací výstup
        test_df = pd.DataFrame({
            'CONTRACT': ['CZ21'],
            'CUSTOMER_NO': ['505992'],
            'CATALOG_NO': ['TEST'],
            'QTY': [1],
            'DATED': ['01.01.2025'],
            'PRICE': [0],
            'CURRENCY_CODE': ['CZK'],
            'CUSTOMER_PO_NO': ['DESCON_TEST'],
            'W': [None],
            'L': [None]
        })
        
        test_df.to_csv(output_path, index=False, sep=';', encoding='utf-8-sig')
        debug(f"✓ DESCON test výstup uložen: {output_path}")
        
        return output_path
        
    except Exception as e:
        debug(f"✗ Chyba v process_descon_order: {e}")
        raise ValueError(f"Chyba při zpracování DESCON objednávky: {e}")

# ==============================================================================
# ČÁST 4: Hlavní GUI aplikace
# ==============================================================================

def start_main_application():
    """Spuštění hlavní GUI aplikace"""
    try:
        debug("Vytvářím hlavní okno...")
        
        root = tk.Tk()
        root.title("Nástroj pro migraci objednávek v7.1 (Debug Edition)")
        root.geometry("500x250")
        root.resizable(False, False)
        
        # Vycentruj okno
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
        y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
        root.geometry(f"+{x}+{y}")
        
        debug("✓ Hlavní okno vytvořeno")
        
        # Hlavní frame
        main_frame = tk.Frame(root, padx=20, pady=20)
        main_frame.pack(expand=True, fill=tk.BOTH)
        
        # Nadpis
        title_label = tk.Label(main_frame, text="Migrace Objednávek v7.1", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 5))
        
        subtitle_label = tk.Label(main_frame, text="Debug Edition s chybovými hlášeními", font=("Arial", 10), fg="gray")
        subtitle_label.pack(pady=(0, 15))
        
        # Instrukce
        instruction_label = tk.Label(main_frame, text="Vyberte typ objednávky ke zpracování:", font=("Arial", 12))
        instruction_label.pack(pady=(0, 15))
        
        # Tlačítka
        btn_labara = tk.Button(main_frame, 
                              text="Zpracovat objednávku LABARA (CSV)", 
                              command=lambda: select_and_process_labara(root),
                              width=45, height=2, bg='#D4EDDA', font=("Arial", 10))
        btn_labara.pack(pady=5)
        
        btn_descon = tk.Button(main_frame, 
                              text="Zpracovat objednávku DESCON (Excel)", 
                              command=lambda: select_and_process_descon(root),
                              width=45, height=2, bg='#CCE5FF', font=("Arial", 10))
        btn_descon.pack(pady=5)
        
        # Debug info
        debug_label = tk.Label(main_frame, text=f"Debug logy: ~/Documents/Migrace_Debug/", font=("Arial", 8), fg="gray")
        debug_label.pack(pady=(15, 0))
        
        debug("✓ GUI komponenty vytvořeny")
        
        # Event handling pro zavření okna
        def on_closing():
            debug("Uživatel zavírá aplikaci")
            root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        debug("Spouštím mainloop...")
        root.mainloop()
        debug("Mainloop ukončen")
        
    except Exception as e:
        debug(f"✗ Chyba v start_main_application: {e}")
        debug(f"Traceback: {traceback.format_exc()}")
        raise

def select_and_process_labara(root):
    """Zpracování LABARA objednávky s GUI"""
    try:
        debug("=== LABARA WORKFLOW ===")
        
        # Výběr souboru
        filepath = filedialog.askopenfilename(
            title="Vyberte CSV soubor s objednávkou LABARA", 
            filetypes=[("CSV soubory", "*.csv"), ("Všechny soubory", "*.*")]
        )
        
        if not filepath:
            debug("Uživatel zrušil výběr souboru")
            return
            
        debug(f"Vybrán soubor: {filepath}")
        
        # Zadání údajů
        dialog = InputDialog(root, "Zadejte údaje z PDF objednávky")
        if not dialog.result:
            debug("Uživatel zrušil zadání údajů")
            return
            
        dated, customer_no = dialog.result
        debug(f"Zadané údaje: datum={dated}, zákazník={customer_no}")
        
        # Najdi katalog
        catalog_path = find_catalog_file()
        if not catalog_path:
            messagebox.showerror("Chyba", "Katalogový soubor nebyl nalezen!\n\nZkontrolujte, že je soubor 'ALDE_katalog_polozek.xlsx' ve stejné složce jako aplikace.")
            return
        
        # Zpracování
        try:
            debug("Spouštím zpracování LABARA...")
            output_path = process_labara_order(filepath, catalog_path, dated, customer_no)
            
            # Úspěch
            success_msg = f"Zpracování LABARA dokončeno!\n\nVýstup byl uložen do:\n{output_path}\n\nChcete otevřít výstupní složku?"
            
            if messagebox.askyesno("Hotovo", success_msg):
                # Pokus o otevření složky
                output_folder = os.path.dirname(output_path)
                try:
                    os.startfile(output_folder)  # Windows
                except:
                    try:
                        os.system(f'explorer "{output_folder}"')  # Windows fallback
                    except:
                        debug("Nepodařilo se otevřít složku")
                        
        except Exception as e:
            debug(f"✗ Chyba při zpracování LABARA: {e}")
            messagebox.showerror("Chyba při zpracování", f"Vyskytla se chyba při zpracování:\n\n{e}\n\nVíce informací v debug logu.")
            
    except Exception as e:
        debug(f"✗ Chyba v select_and_process_labara: {e}")
        messagebox.showerror("Neočekávaná chyba", f"Vyskytla se neočekávaná chyba:\n\n{e}")

def select_and_process_descon(root):
    """Zpracování DESCON objednávky s GUI"""
    try:
        debug("=== DESCON WORKFLOW ===")
        
        # Výběr souboru
        filepath = filedialog.askopenfilename(
            title="Vyberte Excel soubor s objednávkou DESCON", 
            filetypes=[("Excel soubory", "*.xlsx"), ("Všechny soubory", "*.*")]
        )
        
        if not filepath:
            debug("Uživatel zrušil výběr souboru")
            return
            
        debug(f"Vybrán soubor: {filepath}")
        
        # Najdi katalog
        catalog_path = find_catalog_file()
        if not catalog_path:
            messagebox.showerror("Chyba", "Katalogový soubor nebyl nalezen!")
            return
        
        # Zpracování
        try:
            debug("Spouštím zpracování DESCON...")
            output_path = process_descon_order(filepath, catalog_path)
            
            # Úspěch
            success_msg = f"Zpracování DESCON dokončeno!\n\nVýstup byl uložen do:\n{output_path}\n\nChcete otevřít výstupní složku?"
            
            if messagebox.askyesno("Hotovo", success_msg):
                output_folder = os.path.dirname(output_path)
                try:
                    os.startfile(output_folder)
                except:
                    debug("Nepodařilo se otevřít složku")
                        
        except Exception as e:
            debug(f"✗ Chyba při zpracování DESCON: {e}")
            messagebox.showerror("Chyba při zpracování", f"Vyskytla se chyba při zpracování:\n\n{e}\n\nVíce informací v debug logu.")
            
    except Exception as e:
        debug(f"✗ Chyba v select_and_process_descon: {e}")
        messagebox.showerror("Neočekávaná chyba", f"Vyskytla se neočekávaná chyba:\n\n{e}")

# ==============================================================================
# HLAVNÍ VSTUPNÍ BOD
# ==============================================================================

if __name__ == "__main__":
    try:
        # Spuštění s kompletním error handlingem
        safe_main()
    except KeyboardInterrupt:
        debug("Aplikace přerušena uživatelem (Ctrl+C)")
        print("\nAplikace byla přerušena.")
    except Exception as e:
        debug(f"NEOČEKÁVANÁ CHYBA: {e}")
        debug(f"Traceback: {traceback.format_exc()}")
        print(f"NEOČEKÁVANÁ CHYBA: {e}")
        print("Více informací v ~/Documents/Migrace_Debug/migrace_debug.txt")
        input("Stiskněte Enter pro ukončení...")
    finally:
        debug("=== PROGRAM UKONČEN ===")