#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST LABARA OPRAVY
==================
Testovací script pro ověření oprav LABARA zpracování
"""

import pandas as pd
import os
import sys

# Přidáme cestu k našemu modulu
sys.path.append('.')

try:
    from migrace_standalone_final import process_labara_order, get_embedded_catalog
    print("✅ Modul úspěšně načten")
except ImportError as e:
    print(f"❌ Chyba při načítání modulu: {e}")
    exit(1)

def create_test_labara_csv():
    """Vyt<PERSON>ř<PERSON> testovací LABARA CSV soubor."""
    test_data = {
        'číslo zboží': ['001', '002', '003', '004'],
        'množství': ['5 ks', '10 ks', '2 ks', '1 ks'],
        'název': [
            '5083 litá přířez 10,00 - 100 x 200',
            '5083 litá frézovaná tloušťka 15,00 - 150 x 300', 
            '6061T651 válcovaná 20,00 - 200 x 400',
            '7075T651 25,00 mm - 250 x 500'
        ]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_labara.csv'
    df.to_csv(test_file, index=False, sep=';', encoding='windows-1250')
    print(f"✅ Vytvořen testovací soubor: {test_file}")
    return test_file

def test_labara_processing():
    """Testuje LABARA zpracování."""
    print("\n" + "="*60)
    print("🧪 TESTOVÁNÍ LABARA ZPRACOVÁNÍ")
    print("="*60)
    
    # Vytvoř testovací soubor
    test_file = create_test_labara_csv()
    
    try:
        # Test zpracování
        print(f"\n📊 Testování souboru: {test_file}")
        result_df = process_labara_order(
            order_path=test_file,
            dated_input="01.08.2025", 
            customer_no_input="25555308"
        )
        
        print(f"\n✅ VÝSLEDEK:")
        print(f"   • Zpracováno: {len(result_df)} položek")
        
        if len(result_df) > 0:
            print(f"   • Sloupce: {list(result_df.columns)}")
            print(f"\n📋 UKÁZKA VÝSTUPU:")
            print(result_df.to_string(index=False))
            
            # Uložení výsledku
            output_file = 'test_labara_output.csv'
            result_df.to_csv(output_file, index=False, sep=';', encoding='utf-8-sig')
            print(f"\n💾 Výstup uložen do: {output_file}")
        else:
            print("   ❌ ŽÁDNÉ PLATNÉ POLOŽKY!")
            
    except Exception as e:
        print(f"❌ CHYBA PRI ZPRACOVÁNÍ: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Úklid
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 Smazán testovací soubor: {test_file}")

def test_catalog_info():
    """Zobrazí informace o katalogu."""
    print("\n" + "="*60)
    print("📚 INFORMACE O KATALOGU")
    print("="*60)
    
    catalog_df = get_embedded_catalog()
    print(f"📊 Celkem položek v katalogu: {len(catalog_df)}")
    
    # Ukázka prvních 10 položek
    print(f"\n📋 UKÁZKA KATALOGU (prvních 10 položek):")
    print(catalog_df.head(10).to_string(index=False))
    
    # Statistiky materiálů
    materials = catalog_df['DESCRIPTION'].str.extract(r'(\d{4}|\w+)').iloc[:, 0].value_counts()
    print(f"\n📈 TOP 10 MATERIÁLŮ:")
    print(materials.head(10).to_string())

if __name__ == "__main__":
    print("🚀 SPOUŠTÍM TEST LABARA OPRAV")
    print("="*60)
    
    # Test informací o katalogu
    test_catalog_info()
    
    # Test LABARA zpracování
    test_labara_processing()
    
    print("\n" + "="*60)
    print("✅ TEST DOKONČEN")
    print("="*60)
    
    input("\nStiskněte Enter pro ukončení...")
