================================================================================
MIGRACE OBJEDNÁVEK - STANDALONE VERZE S VESTAVĚNÝM KATALOGEM
================================================================================

DŮLEŽITÉ: Tyto verze mají katalog ALDE zabudovaný přímo v kódu!

ČO SPUSTIŤ:
-----------
1. SPUSTIT_KOMPLETNI.bat      - KOMPLETNÍ katalog (508 položek) ⭐ DOPORUČENO
2. SPUSTIT_STANDALONE.bat     - základní katalog (50 položek) 
3. main_complete_508.py       - Python soubor s KOMPLETNÍM katalogem
4. main_standalone_fixed.py   - Python soubor se základním katalogem

ŽÁDNÝ EXTERNÍ SOUBOR KATALOGY NENÍ POTŘEBA!
Soubor "ALDE_katalog_polozek.xlsx" můžete smazat.

TESTOVACÍ DATA:
--------------
- IMPA_Test_Data/ - obsahuje testovací soubory IMPA1.xlsx, IMPA2.xlsx, IMPA3.xlsx

POUŽITÍ:
--------
1. Spusťte SPUSTIT_KOMPLETNI.bat (DOPORUČENO - 508 položek)
2. Vyberte typ objednávky:
   ✅ LABARA - CSV soubory, zadejte datum a zákazníka
   ✅ IMPA - Excel soubory, zadejte datum, zákazníka a PO číslo  
   ✅ DESCON - Excel soubory, automatické datum a pevný zákazník
3. Vyberte soubor k zpracování
4. Vyplňte požadované údaje v dialogu
5. Výstup se uloží do Documents/Migrace_Vystupy/

VERZE: Standalone s embedded katalogem (508 položek)
DATUM: 2025-07-02